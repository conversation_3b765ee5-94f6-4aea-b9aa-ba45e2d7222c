//! # TAMTIL: Zero-Copy Actor System
//!
//! A production-ready actor system built on rkyv's zero-copy serialization,
//! following <PERSON>'s actor pattern with action->reaction and remember->recall.
//!
//! Actors now have integrated memories with graph capabilities, offering a builder
//! pattern for developers to extend actors with actions rather than creating actors from scratch.

// Core module - everything is now in actor.rs
pub mod actor;

// Re-export all types from actor module
pub use actor::*;
