//! # TAMTIL: Zero-Copy Actor System
//!
//! A production-ready actor system built on rkyv's zero-copy serialization,
//! following <PERSON>'s actor pattern with action->reaction and remember->recall.
//!
//! Actors now have integrated memories with graph capabilities, offering a builder
//! pattern for developers to extend actors with actions rather than creating actors from scratch.

// Core modules
pub mod actor;
pub mod context;
pub mod core;
pub mod messenger;
pub mod platform;

// Re-export core types for convenience
pub use core::*;

// Re-export key types from modules
pub use actor::{Actor, ActorBuilder, ActorMemories, BuiltActor};
pub use platform::Actors;
