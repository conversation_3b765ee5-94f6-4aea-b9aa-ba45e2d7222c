//! Platform module - Core single-node system management
//! 
//! The Platform is the root of TAMTIL's actor hierarchy, managing the entire system.
//! It handles actor spawning, lifecycle management, and provides the foundation
//! for distributed consensus when multiple nodes are added.

use crate::core::{<PERSON><PERSON><PERSON>rror, <PERSON><PERSON>R<PERSON>ult, ActorId};
use crate::Actor;
use crate::actor::{ActorHandle, ActorTask, ActorMessage, ActorMemories};
use async_trait::async_trait;
use std::{
    any::TypeId,
    collections::HashMap,
    sync::Arc,
};
use tokio::sync::{mpsc, oneshot, RwLock};

/// Type-erased actor handle for the registry
#[async_trait]
pub trait ErasedActorHandle: Send + Sync {
    fn actor_id(&self) -> &ActorId;
    async fn shutdown(&self) -> TamtilResult<()>;
}

/// Actor registry that manages all actors in the system
pub struct ActorRegistry {
    actors: RwLock<HashMap<ActorId, Box<dyn ErasedActorHandle>>>,
    type_registry: RwLock<HashMap<ActorId, TypeId>>,
}

impl ActorRegistry {
    pub fn new() -> Self {
        Self {
            actors: RwLock::new(HashMap::new()),
            type_registry: RwLock::new(HashMap::new()),
        }
    }

    /// Register an actor handle
    pub async fn register<T: Actor>(&self, handle: ActorHandle<T>) -> TamtilResult<()> {
        let actor_id = handle.id.clone();
        let type_id = TypeId::of::<T>();

        let mut actors = self.actors.write().await;
        let mut types = self.type_registry.write().await;

        actors.insert(actor_id.clone(), Box::new(handle));
        types.insert(actor_id, type_id);

        Ok(())
    }

    /// Check if an actor exists and has the correct type
    pub async fn exists<T: Actor>(&self, actor_id: &ActorId) -> bool {
        let types = self.type_registry.read().await;
        if let Some(&type_id) = types.get(actor_id) {
            type_id == TypeId::of::<T>()
        } else {
            false
        }
    }

    /// Shutdown an actor
    pub async fn shutdown(&self, actor_id: &ActorId) -> TamtilResult<()> {
        let mut actors = self.actors.write().await;
        let mut types = self.type_registry.write().await;

        if let Some(handle) = actors.remove(actor_id) {
            types.remove(actor_id);
            handle.shutdown().await?;
            tracing::info!("Actor {} shutdown and removed from registry", actor_id.as_str());
        }

        Ok(())
    }

    /// List all registered actors
    pub async fn list_actors(&self) -> Vec<ActorId> {
        let actors = self.actors.read().await;
        actors.keys().cloned().collect()
    }
}

/// The main Actors interface - this is what developers use
#[derive(Clone)]
pub struct Actors {
    registry: Arc<ActorRegistry>,
}

impl Actors {
    pub fn new() -> Self {
        Self {
            registry: Arc::new(ActorRegistry::new()),
        }
    }

    /// Spawn a new actor and register it
    /// Each actor now has its own integrated memories
    pub async fn spawn<T: Actor>(&self, actor_id: ActorId, actor: T) -> TamtilResult<ActorHandle<T>> {
        let (sender, receiver) = mpsc::channel::<ActorMessage<T::Action>>(1024);

        // Create integrated memories for this actor
        let actor_memories = ActorMemories::new().await?;

        // Create the task
        let task = ActorTask::new(
            actor_id.clone(),
            actor,
            receiver,
            self.clone(),
            actor_memories,
        );

        // Spawn the task
        let task_id = actor_id.clone();
        tokio::spawn(async move {
            if let Err(e) = task.run().await {
                tracing::error!("Actor {} task failed: {}", task_id.as_str(), e);
            }
        });

        // Create the handle
        let handle = ActorHandle::new(actor_id.clone(), sender.clone());

        // Register the handle (create a second handle for registration)
        let registry_handle: ActorHandle<T> = ActorHandle::new(actor_id.clone(), sender);
        self.registry.register(registry_handle).await?;

        tracing::info!("Actor {} spawned and registered", actor_id.as_str());
        Ok(handle)
    }

    /// Get an actor proxy for communication
    pub fn actor(&self, actor_id: impl Into<ActorId>) -> ActorProxy {
        ActorProxy::new(actor_id.into(), self.registry.clone())
    }

    /// Shutdown all actors
    pub async fn shutdown_all(&self) -> TamtilResult<()> {
        let actor_ids = self.registry.list_actors().await;
        for actor_id in actor_ids {
            if let Err(e) = self.registry.shutdown(&actor_id).await {
                tracing::error!("Failed to shutdown actor {}: {}", actor_id.as_str(), e);
            }
        }
        Ok(())
    }
}

/// Actor proxy for type-erased communication
pub struct ActorProxy {
    target_id: ActorId,
    registry: Arc<ActorRegistry>,
}

impl ActorProxy {
    pub fn new(target_id: ActorId, registry: Arc<ActorRegistry>) -> Self {
        Self { target_id, registry }
    }

    /// Send an action to an actor (type-erased)
    pub async fn act<A, R>(&self, action: A) -> TamtilResult<Vec<u8>>
    where
        A: rkyv::Archive + Send + Sync + 'static + std::fmt::Debug,
        R: crate::Reaction + Clone + Default + std::fmt::Debug,
    {
        // For now, this is a simplified implementation
        // In a complete system, we'd need proper type-erased message routing
        Err(TamtilError::Communication {
            message: "Type-erased actor communication not yet implemented".to_string()
        })
    }
}


