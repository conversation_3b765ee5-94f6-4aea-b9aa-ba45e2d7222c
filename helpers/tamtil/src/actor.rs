//! Actor module - <PERSON>'s pattern with integrated memories
//!
//! This module implements the core actor system following <PERSON>'s pattern:
//! - Separate Task and Handle structs
//! - Oneshot channels for responses
//! - tokio::spawn in handle constructor
//! - Zero-copy serialization with rkyv
//! - Integrated graph-based memories with petgraph-inspired algorithms

// All core types are now defined in this module
use async_trait::async_trait;
use rkyv::{Archive, Serialize, Deserialize, api::high::{to_bytes, from_bytes}, rancor::Error as RancorError};
use std::{collections::{HashMap, VecDeque, HashSet}, sync::Arc, fmt};
use tokio::sync::{mpsc, oneshot, RwLock};

/// TAMTIL Error types
#[derive(Debug, thiserror::Error)]
pub enum TamtilError {
    #[error("Serialization error: {message}")]
    Serialization { message: String },

    #[error("Deserialization error: {message}")]
    Deserialization { message: String },

    #[error("Actor not found: {id}")]
    ActorNotFound { id: String },

    #[error("Communication error: {message}")]
    Communication { message: String },

    #[error("Invalid operation: {message}")]
    InvalidOperation { message: String },
}

/// Result type for TAMTIL operations
pub type TamtilResult<T> = Result<T, TamtilError>;

/// Actor ID for hierarchical addressing
#[derive(Debug, Clone, Hash, Archive, Serialize, Deserialize)]
#[rkyv(derive(Debug))]
pub struct ActorId(pub String);

impl PartialEq for ActorId {
    fn eq(&self, other: &Self) -> bool {
        self.0 == other.0
    }
}

impl Eq for ActorId {}

impl ActorId {
    pub fn new(id: impl Into<String>) -> Self {
        Self(id.into())
    }

    pub fn as_str(&self) -> &str {
        &self.0
    }

    /// Get parent actor ID
    pub fn parent(&self) -> Option<Self> {
        self.0.rfind('/').map(|pos| Self(self.0[..pos].to_string()))
    }

    /// Get the depth in the hierarchy (0 = root)
    pub fn depth(&self) -> usize {
        if self.0.is_empty() { 0 } else { self.0.matches('/').count() }
    }

    /// Check if this is a child of another actor
    pub fn is_child_of(&self, parent: &ActorId) -> bool {
        self.0.starts_with(parent.as_str())
            && self.0.len() > parent.0.len()
            && self.0.chars().nth(parent.0.len()) == Some('/')
    }
}

/// Memory value wrapper for rkyv serialization
#[derive(Debug, Clone, PartialEq, Archive, Serialize, Deserialize)]
#[rkyv(derive(Debug))]
pub struct MemoryValue {
    /// Raw rkyv serialized bytes - zero-copy access only
    pub bytes: Vec<u8>,
}

impl MemoryValue {
    /// Create MemoryValue from any rkyv-serializable type using the high-level API
    pub fn new<T>(value: &T) -> TamtilResult<Self>
    where
        T: Archive + for<'a> rkyv::Serialize<rkyv::api::high::HighSerializer<rkyv::util::AlignedVec, rkyv::ser::allocator::ArenaHandle<'a>, rkyv::rancor::Error>>
    {
        let bytes = rkyv::api::high::to_bytes::<rkyv::rancor::Error>(value)
            .map_err(|e| TamtilError::Serialization {
                message: format!("Failed to serialize to MemoryValue: {}", e)
            })?;
        Ok(Self { bytes: bytes.to_vec() })
    }

    /// Convenience constructors
    pub fn string(s: &str) -> TamtilResult<Self> {
        Self::new(&s.to_string())
    }

    pub fn number(n: f64) -> TamtilResult<Self> {
        Self::new(&n)
    }

    pub fn boolean(b: bool) -> TamtilResult<Self> {
        Self::new(&b)
    }
}

/// Memory operations for atomic state changes
#[derive(Debug, Clone, Archive, Serialize, Deserialize)]
#[rkyv(derive(Debug))]
pub enum MemoryOperation {
    Create { key: String, value: MemoryValue },
    Update { key: String, value: MemoryValue },
    Delete { key: String },
    Link { from: String, to: String, relation: String },
    Unlink { from: String, to: String, relation: String },
}

/// Action trait - represents operations that can be performed
pub trait Action: Send + Sync + 'static {
    type Reaction: Reaction;

    fn act(
        &self,
        actors: &Actors,
        memories: &ActorMemories,
    ) -> impl std::future::Future<Output = TamtilResult<Self::Reaction>> + Send
    where
        Self: Send;
}

/// The Reaction trait is central to TAMTIL's design.
/// Every actor action produces a reaction that can be remembered.
pub trait Reaction: Archive + Send + Sync + 'static + for<'a> rkyv::Serialize<rkyv::api::high::HighSerializer<rkyv::util::AlignedVec, rkyv::ser::allocator::ArenaHandle<'a>, rkyv::rancor::Error>> {
    /// Convert this reaction into memory operations for persistence
    fn remember(&self) -> Vec<MemoryOperation>;
}

/// Actor system for managing multiple actors
#[derive(Clone)]
pub struct Actors {
    actors: Arc<RwLock<HashMap<ActorId, Box<dyn ErasedActorHandle>>>>,
}

impl Actors {
    pub async fn new() -> TamtilResult<Self> {
        Ok(Self {
            actors: Arc::new(RwLock::new(HashMap::new())),
        })
    }

    pub async fn spawn<T: Actor>(&self, id: ActorId, actor: T) -> TamtilResult<()> {
        let (sender, receiver) = mpsc::channel(100);
        let memories = ActorMemories::new().await?;

        let task = ActorTask::new(id.clone(), actor, receiver, self.clone(), memories);
        tokio::spawn(async move {
            task.run().await;
        });

        let handle: ActorHandle<T> = ActorHandle::new(id.clone(), sender);
        let mut actors = self.actors.write().await;
        actors.insert(id, Box::new(handle));

        Ok(())
    }

    pub fn actor(&self, id: &ActorId) -> ActorProxy {
        ActorProxy {
            id: id.clone(),
            actors: self.actors.clone(),
        }
    }
}

/// Proxy for accessing actors
pub struct ActorProxy {
    id: ActorId,
    actors: Arc<RwLock<HashMap<ActorId, Box<dyn ErasedActorHandle>>>>,
}

impl ActorProxy {
    pub async fn act<A: Action + fmt::Debug>(&self, action: A) -> TamtilResult<Vec<u8>> {
        // For now, return a simple serialized response
        // In a full implementation, this would route to the actual actor
        let response = format!("Action processed: {:?}", action);
        Ok(response.into_bytes())
    }

    pub async fn get_built_actor<A: Action + fmt::Debug>(&self) -> Option<BuiltActor<A>> {
        // This is a placeholder - in a real implementation, we'd return the actual actor
        None::<BuiltActor<A>>
    }
}

/// Trait for type-erased actor handles
#[async_trait]
pub trait ErasedActorHandle: Send + Sync {
    fn actor_id(&self) -> &ActorId;
    async fn shutdown(&self) -> TamtilResult<()>;
}

/// Fault-tolerant storage for reactions (event sourcing)
/// Inspired by OmniPaxos storage patterns
#[derive(Clone)]
pub struct ReactionLog {
    /// Sequence of reactions (the single source of truth)
    reactions: Arc<RwLock<Vec<Vec<u8>>>>, // serialized reactions
    /// Current decided index (reactions up to this index are committed)
    decided_idx: Arc<RwLock<usize>>,
    /// Snapshot of projected state at compacted_idx
    snapshot: Arc<RwLock<Option<Vec<u8>>>>,
    /// Index up to which reactions have been compacted into snapshot
    compacted_idx: Arc<RwLock<usize>>,
}

impl ReactionLog {
    /// Create a new reaction log
    pub fn new() -> Self {
        Self {
            reactions: Arc::new(RwLock::new(Vec::new())),
            decided_idx: Arc::new(RwLock::new(0)),
            snapshot: Arc::new(RwLock::new(None)),
            compacted_idx: Arc::new(RwLock::new(0)),
        }
    }

    /// Append a reaction to the log (atomic operation)
    pub async fn append_reaction(&self, reaction_bytes: Vec<u8>) -> TamtilResult<usize> {
        let mut reactions = self.reactions.write().await;
        reactions.push(reaction_bytes);
        let new_idx = reactions.len();

        // Auto-commit for single node (in distributed mode, this would be consensus-driven)
        let mut decided = self.decided_idx.write().await;
        *decided = new_idx;

        tracing::debug!("Appended reaction at index {}", new_idx);
        Ok(new_idx)
    }

    /// Get reactions from a specific index (for replay/recovery)
    pub async fn get_reactions_from(&self, from_idx: usize) -> Vec<Vec<u8>> {
        let reactions = self.reactions.read().await;
        let decided = *self.decided_idx.read().await;

        if from_idx >= reactions.len() {
            return Vec::new();
        }

        // Only return decided reactions
        let end_idx = std::cmp::min(decided, reactions.len());
        reactions[from_idx..end_idx].to_vec()
    }

    /// Get the current decided index
    pub async fn get_decided_idx(&self) -> usize {
        *self.decided_idx.read().await
    }

    /// Compact the log by creating a snapshot (inspired by OmniPaxos compaction)
    pub async fn compact(&self, up_to_idx: usize, snapshot_data: Vec<u8>) -> TamtilResult<()> {
        let decided = *self.decided_idx.read().await;

        if up_to_idx > decided {
            return Err(TamtilError::InvalidOperation {
                message: format!("Cannot compact beyond decided index {} (requested: {})", decided, up_to_idx)
            });
        }

        // Store snapshot
        let mut snapshot = self.snapshot.write().await;
        *snapshot = Some(snapshot_data);

        // Update compacted index
        let mut compacted = self.compacted_idx.write().await;
        *compacted = up_to_idx;

        // Remove compacted reactions
        let mut reactions = self.reactions.write().await;
        if up_to_idx > 0 && up_to_idx <= reactions.len() {
            reactions.drain(0..up_to_idx);
        }

        tracing::info!("Compacted reaction log up to index {}", up_to_idx);
        Ok(())
    }

    /// Get the current snapshot
    pub async fn get_snapshot(&self) -> Option<Vec<u8>> {
        self.snapshot.read().await.clone()
    }

    /// Get the compacted index
    pub async fn get_compacted_idx(&self) -> usize {
        *self.compacted_idx.read().await
    }
}

/// Graph algorithms adapted from petgraph for TAMTIL memories
pub struct GraphAlgorithms;

impl GraphAlgorithms {
    /// Dijkstra's shortest path algorithm adapted for memory graphs
    pub async fn dijkstra_shortest_path(
        memories: &ActorMemories,
        start: &str,
        goal: Option<&str>,
        max_distance: f64,
    ) -> HashMap<String, (f64, Vec<String>)> {
        let mut distances = HashMap::new();
        let mut paths = HashMap::new();
        let mut visited = HashSet::new();
        let mut heap = std::collections::BinaryHeap::new();

        // Start with the initial node
        distances.insert(start.to_string(), 0.0);
        paths.insert(start.to_string(), vec![start.to_string()]);
        heap.push(std::cmp::Reverse((0.0 as i64, start.to_string())));

        while let Some(std::cmp::Reverse((dist_times_1000, current))) = heap.pop() {
            let current_dist = dist_times_1000 as f64 / 1000.0;

            if visited.contains(&current) {
                continue;
            }

            if let Some(goal_node) = goal {
                if current == goal_node {
                    break;
                }
            }

            if current_dist > max_distance {
                continue;
            }

            visited.insert(current.clone());

            // Get connected memories
            let connected = memories.get_connected(&current).await;
            for neighbor in connected {
                if visited.contains(&neighbor) {
                    continue;
                }

                let edge_weight = 1.0; // Default weight, could be customized
                let new_dist = current_dist + edge_weight;

                if new_dist <= max_distance {
                    let should_update = match distances.get(&neighbor) {
                        Some(&existing_dist) => new_dist < existing_dist,
                        None => true,
                    };

                    if should_update {
                        distances.insert(neighbor.clone(), new_dist);

                        let mut new_path = paths.get(&current).unwrap().clone();
                        new_path.push(neighbor.clone());
                        paths.insert(neighbor.clone(), new_path);

                        heap.push(std::cmp::Reverse(((new_dist * 1000.0) as i64, neighbor)));
                    }
                }
            }
        }

        // Combine distances and paths
        let mut result = HashMap::new();
        for (node, dist) in distances {
            if let Some(path) = paths.get(&node) {
                result.insert(node, (dist, path.clone()));
            }
        }

        result
    }

    /// Connected components algorithm adapted from petgraph
    pub async fn connected_components(memories: &ActorMemories, start_nodes: &[String]) -> Vec<Vec<String>> {
        let mut visited = HashSet::new();
        let mut components = Vec::new();

        for start_node in start_nodes {
            if visited.contains(start_node) {
                continue;
            }

            let mut component = Vec::new();
            let mut stack = vec![start_node.clone()];

            while let Some(current) = stack.pop() {
                if visited.contains(&current) {
                    continue;
                }

                visited.insert(current.clone());
                component.push(current.clone());

                // Add all connected nodes to the stack
                let connected = memories.get_connected(&current).await;
                for neighbor in connected {
                    if !visited.contains(&neighbor) {
                        stack.push(neighbor);
                    }
                }
            }

            if !component.is_empty() {
                components.push(component);
            }
        }

        components
    }
}

/// Direction for graph traversal (adapted from petgraph)
#[derive(Debug, Clone, Copy, PartialEq, Eq, Archive, rkyv::Serialize, rkyv::Deserialize)]
#[rkyv(derive(Debug))]
pub enum Direction {
    /// Outgoing edges from a memory
    Outgoing,
    /// Incoming edges to a memory
    Incoming,
}

impl Direction {
    /// Return the opposite direction
    pub fn opposite(self) -> Direction {
        match self {
            Direction::Outgoing => Direction::Incoming,
            Direction::Incoming => Direction::Outgoing,
        }
    }
}

/// Edge weight for memory connections
#[derive(Debug, Clone, Archive, rkyv::Serialize, rkyv::Deserialize)]
#[rkyv(derive(Debug))]
pub struct EdgeWeight {
    pub relation: String,
    pub weight: f64,
    pub metadata: HashMap<String, MemoryValue>,
}

impl EdgeWeight {
    pub fn new(relation: String) -> Self {
        Self {
            relation,
            weight: 1.0,
            metadata: HashMap::new(),
        }
    }

    pub fn with_weight(mut self, weight: f64) -> Self {
        self.weight = weight;
        self
    }

    pub fn with_metadata(mut self, key: String, value: MemoryValue) -> Self {
        self.metadata.insert(key, value);
        self
    }
}

/// Path finding algorithms (inspired by petgraph)
#[derive(Debug, Clone)]
pub enum PathAlgorithm {
    /// Breadth-first search
    BFS,
    /// Depth-first search
    DFS,
    /// Simple traversal (current implementation)
    Simple,
}

/// Graph traversal result
#[derive(Debug, Clone)]
pub struct TraversalResult {
    pub memory_id: String,
    pub distance: usize,
    pub path: Vec<String>,
    pub total_weight: f64,
}

/// A Memory is a collection of key-values under one ID
#[derive(Debug, Clone, Archive, rkyv::Serialize, rkyv::Deserialize)]
#[rkyv(derive(Debug))]
pub struct Memory {
    pub id: String,
    pub data: HashMap<String, MemoryValue>,
    pub connections: Vec<String>, // IDs of connected memories
}

impl Memory {
    pub fn new(id: String) -> Self {
        Self {
            id,
            data: HashMap::new(),
            connections: Vec::new(),
        }
    }

    pub fn get(&self, key: &str) -> Option<&MemoryValue> {
        self.data.get(key)
    }

    pub fn set(&mut self, key: String, value: MemoryValue) {
        self.data.insert(key, value);
    }

    pub fn remove(&mut self, key: &str) -> Option<MemoryValue> {
        self.data.remove(key)
    }

    pub fn connect(&mut self, memory_id: String) {
        if !self.connections.contains(&memory_id) {
            self.connections.push(memory_id);
        }
    }

    pub fn disconnect(&mut self, memory_id: &str) {
        self.connections.retain(|id| id != memory_id);
    }
}

/// Strategy for recall operations
#[derive(Debug, Clone)]
pub enum RecallStrategy {
    /// Zero-copy access (fastest, but requires type to implement rkyv traits)
    ZeroCopy,
    /// Safe deserialization (slower but works with any serde type)
    Safe,
    /// Raw bytes access
    Raw,
}

/// Integrated memories system for actors with fault tolerance
#[derive(Clone)]
pub struct ActorMemories {
    memories: Arc<RwLock<HashMap<String, Vec<u8>>>>, // memory_id -> serialized Memory
    /// Fault-tolerant reaction log (single source of truth)
    reaction_log: ReactionLog,
    /// Last applied reaction index (for recovery)
    last_applied_idx: Arc<RwLock<usize>>,
}

impl ActorMemories {
    /// Create a new memories system with fault tolerance
    pub async fn new() -> TamtilResult<Self> {
        Ok(Self {
            memories: Arc::new(RwLock::new(HashMap::new())),
            reaction_log: ReactionLog::new(),
            last_applied_idx: Arc::new(RwLock::new(0)),
        })
    }

    /// Recover from reaction log (replay all reactions to rebuild state)
    pub async fn recover(&self) -> TamtilResult<()> {
        let last_applied = *self.last_applied_idx.read().await;
        let reactions = self.reaction_log.get_reactions_from(last_applied).await;

        tracing::info!("Recovering from {} reactions starting at index {}", reactions.len(), last_applied);

        for (idx, _reaction_bytes) in reactions.iter().enumerate() {
            // Deserialize reaction and apply its memory operations
            // This is a simplified recovery - in practice, we'd need to know the reaction type
            // For now, we'll implement a basic recovery mechanism
            let reaction_idx = last_applied + idx + 1;

            // TODO: Implement proper reaction deserialization and replay
            // For now, we'll just update the last applied index
            let mut last_applied_lock = self.last_applied_idx.write().await;
            *last_applied_lock = reaction_idx;
        }

        tracing::info!("Recovery completed. Last applied index: {}", *self.last_applied_idx.read().await);
        Ok(())
    }

    /// Get the reaction log for advanced operations
    pub fn reaction_log(&self) -> &ReactionLog {
        &self.reaction_log
    }

    /// Get or create a memory by ID
    async fn get_or_create_memory(&self, memory_id: &str) -> TamtilResult<Memory> {
        let memories = self.memories.read().await;

        if let Some(memory_bytes) = memories.get(memory_id) {
            // Deserialize existing memory
            from_bytes::<Memory, RancorError>(memory_bytes)
                .map_err(|e| TamtilError::Deserialization {
                    message: format!("Failed to deserialize memory {}: {}", memory_id, e)
                })
        } else {
            // Create new memory
            Ok(Memory::new(memory_id.to_string()))
        }
    }

    /// Save a memory back to storage
    async fn save_memory(&self, memory: &Memory) -> TamtilResult<()> {
        let memory_bytes = to_bytes::<RancorError>(memory)
            .map_err(|e| TamtilError::Serialization {
                message: format!("Failed to serialize memory {}: {}", memory.id, e)
            })?;

        let mut memories = self.memories.write().await;
        memories.insert(memory.id.clone(), memory_bytes.to_vec());
        Ok(())
    }

    /// Remember memory operations directly (lower-level API)
    pub async fn remember(&self, operations: Vec<MemoryOperation>) -> TamtilResult<()> {
        self.remember_operations(operations).await
    }

    /// Remember a reaction - higher level API that accepts reactions and stores them using reaction.remember()
    /// This is the fault-tolerant event sourcing approach - reactions are the single source of truth
    pub async fn remember_reaction<R>(&self, reaction: &R) -> TamtilResult<()>
    where
        R: Reaction + for<'a> rkyv::Serialize<rkyv::api::high::HighSerializer<rkyv::util::AlignedVec, rkyv::ser::allocator::ArenaHandle<'a>, rkyv::rancor::Error>>
    {
        // First, serialize and store the reaction in the reaction log (event sourcing)
        let reaction_bytes = rkyv::api::high::to_bytes::<RancorError>(reaction)
            .map_err(|e| TamtilError::Serialization {
                message: format!("Failed to serialize reaction for event log: {}", e)
            })?;

        let reaction_idx = self.reaction_log.append_reaction(reaction_bytes.to_vec()).await?;

        // Then apply the reaction's memory operations to the projected state
        let operations = reaction.remember();
        let operations_count = operations.len();
        self.remember_operations(operations).await?;

        // Update the last applied index
        let mut last_applied = self.last_applied_idx.write().await;
        *last_applied = reaction_idx;

        tracing::debug!("Remembered reaction at index {} with {} operations", reaction_idx, operations_count);
        Ok(())
    }

    /// Remember multiple reactions atomically
    pub async fn remember_reactions<R: Reaction>(&self, reactions: &[R]) -> TamtilResult<()> {
        let mut all_operations = Vec::new();
        for reaction in reactions {
            all_operations.extend(reaction.remember());
        }
        self.remember_operations(all_operations).await
    }

    /// Remember (persist) memory operations atomically - lower level API
    pub async fn remember_operations(&self, operations: Vec<MemoryOperation>) -> TamtilResult<()> {
        if operations.is_empty() {
            return Ok(());
        }

        // Collect all affected memories first to validate operations
        let mut affected_memories = HashMap::new();

        // Pre-validate all operations and collect affected memories
        for operation in &operations {
            match operation {
                MemoryOperation::Create { key, .. } |
                MemoryOperation::Update { key, .. } |
                MemoryOperation::Delete { key } => {
                    let (memory_id, _) = if let Some(pos) = key.find(':') {
                        (&key[..pos], &key[pos + 1..])
                    } else {
                        ("default", key.as_str())
                    };

                    if !affected_memories.contains_key(memory_id) {
                        let memory = self.get_or_create_memory(memory_id).await?;
                        affected_memories.insert(memory_id.to_string(), memory);
                    }
                }
                MemoryOperation::Link { from, to, .. } |
                MemoryOperation::Unlink { from, to, .. } => {
                    if !affected_memories.contains_key(from) {
                        let memory = self.get_or_create_memory(from).await?;
                        affected_memories.insert(from.clone(), memory);
                    }
                    if !affected_memories.contains_key(to) {
                        let memory = self.get_or_create_memory(to).await?;
                        affected_memories.insert(to.clone(), memory);
                    }
                }
            }
        }

        // Apply all operations to the in-memory copies
        for operation in operations {
            match operation {
                MemoryOperation::Create { key, value } | MemoryOperation::Update { key, value } => {
                    let (memory_id, memory_key) = if let Some(pos) = key.find(':') {
                        (&key[..pos], &key[pos + 1..])
                    } else {
                        ("default", key.as_str())
                    };

                    if let Some(memory) = affected_memories.get_mut(memory_id) {
                        memory.set(memory_key.to_string(), value);
                        tracing::debug!("Applied {}:{}", memory_id, memory_key);
                    }
                }
                MemoryOperation::Delete { key } => {
                    let (memory_id, memory_key) = if let Some(pos) = key.find(':') {
                        (&key[..pos], &key[pos + 1..])
                    } else {
                        ("default", key.as_str())
                    };

                    if let Some(memory) = affected_memories.get_mut(memory_id) {
                        memory.remove(memory_key);
                        tracing::debug!("Deleted {}:{}", memory_id, memory_key);
                    }
                }
                MemoryOperation::Link { from, to, relation: _ } => {
                    if let Some(from_memory) = affected_memories.get_mut(&from) {
                        from_memory.connect(to.clone());
                        tracing::debug!("Connected {} -> {}", from, to);
                    }
                }
                MemoryOperation::Unlink { from, to, relation: _ } => {
                    if let Some(from_memory) = affected_memories.get_mut(&from) {
                        from_memory.disconnect(&to);
                        tracing::debug!("Disconnected {} -> {}", from, to);
                    }
                }
            }
        }

        // Atomically save all affected memories
        for (_, memory) in affected_memories {
            self.save_memory(&memory).await?;
        }

        Ok(())
    }

    /// Recall a value by key with zero-copy access using rkyv high-level API
    pub async fn recall<T>(&self, key: &str) -> Option<T>
    where
        T: Archive,
        T::Archived: for<'a> rkyv::bytecheck::CheckBytes<rkyv::api::high::HighValidator<'a, rkyv::rancor::Error>>
            + rkyv::Deserialize<T, rkyv::rancor::Strategy<rkyv::de::pooling::Pool, rkyv::rancor::Error>>
    {
        let (memory_id, memory_key) = if let Some(pos) = key.find(':') {
            (&key[..pos], &key[pos + 1..])
        } else {
            ("default", key)
        };

        if let Ok(memory) = self.get_or_create_memory(memory_id).await {
            if let Some(memory_value) = memory.get(memory_key) {
                // Use rkyv's high-level checked API for safe deserialization
                match rkyv::api::high::from_bytes::<T, RancorError>(&memory_value.bytes) {
                    Ok(value) => Some(value),
                    Err(_) => None,
                }
            } else {
                None
            }
        } else {
            None
        }
    }

    /// Get connected memories (graph traversal)
    pub async fn get_connected(&self, memory_id: &str) -> Vec<String> {
        if let Ok(memory) = self.get_or_create_memory(memory_id).await {
            memory.connections.clone()
        } else {
            Vec::new()
        }
    }

    /// Query the graph using BFS algorithm (adapted from petgraph)
    pub async fn query_graph_bfs(&self, start_memory_id: &str, max_depth: usize) -> Vec<TraversalResult> {
        let mut visited = HashSet::new();
        let mut queue = VecDeque::new();
        let mut results = Vec::new();

        queue.push_back((start_memory_id.to_string(), 0, vec![start_memory_id.to_string()], 0.0));

        while let Some((memory_id, depth, path, total_weight)) = queue.pop_front() {
            if visited.contains(&memory_id) || depth > max_depth {
                continue;
            }

            visited.insert(memory_id.clone());
            results.push(TraversalResult {
                memory_id: memory_id.clone(),
                distance: depth,
                path: path.clone(),
                total_weight,
            });

            if depth < max_depth {
                let connected = self.get_connected(&memory_id).await;
                for connected_id in connected {
                    if !visited.contains(&connected_id) {
                        let mut new_path = path.clone();
                        new_path.push(connected_id.clone());
                        queue.push_back((connected_id, depth + 1, new_path, total_weight + 1.0));
                    }
                }
            }
        }

        results
    }

    /// Clear all memories (useful for testing)
    pub async fn clear(&self) -> TamtilResult<()> {
        let mut memories = self.memories.write().await;
        memories.clear();
        tracing::debug!("Cleared all memories");
        Ok(())
    }
}

/// The core Actor trait following Alice Ryhl's pattern with integrated memories
/// Actions contain the logic and produce reactions when executed
/// The memories API is passed to actions so developers can control when reactions are remembered
#[async_trait]
pub trait Actor: Send + Sync + 'static + std::any::Any {
    type Action: Action + fmt::Debug;

    /// Process an action by delegating to the action's act method
    /// The action contains the logic and produces a reaction
    /// The memories API is passed to the action so developers can remember reactions at the correct time
    async fn act(&mut self, action: Self::Action, actors: &Actors, memories: &ActorMemories) -> TamtilResult<Vec<u8>> {
        // Delegate to the action's act method - the action is responsible for remembering reactions
        let reaction = action.act(actors, memories).await?;

        // Serialize the reaction using rkyv - the reaction is returned but not automatically remembered
        let reaction_bytes = rkyv::api::high::to_bytes::<RancorError>(&reaction)
            .map_err(|e| TamtilError::Serialization {
                message: format!("Failed to serialize reaction: {}", e)
            })?;

        Ok(reaction_bytes.to_vec())
    }
}

/// Type-safe message envelope that preserves rkyv's type information
#[derive(Archive, rkyv::Serialize, rkyv::Deserialize)]
pub struct TypedMessage<A> {
    pub action: A,
    pub request_id: Option<u64>, // None for tell, Some for ask
    pub sender_id: ActorId,
}

/// Internal actor messages for lifecycle management
pub enum ActorMessage<A: Action> {
    TypedAction(TypedMessage<A>, oneshot::Sender<TamtilResult<Vec<u8>>>),
    Shutdown,
}

/// Actor Task - the actual actor implementation following Alice Ryhl's pattern
pub struct ActorTask<T: Actor> {
    pub id: ActorId,
    pub actor: T,
    pub receiver: mpsc::Receiver<ActorMessage<T::Action>>,
    pub actors: Actors,
    pub memories: ActorMemories,
}

impl<T: Actor> ActorTask<T> {
    pub fn new(
        id: ActorId,
        actor: T,
        receiver: mpsc::Receiver<ActorMessage<T::Action>>,
        actors: Actors,
        memories: ActorMemories,
    ) -> Self {
        Self {
            id,
            actor,
            receiver,
            actors,
            memories,
        }
    }

    /// Run the actor task - this is where the magic happens
    pub async fn run(mut self) -> TamtilResult<()> {
        tracing::info!("Actor {} starting", self.id.as_str());

        while let Some(message) = self.receiver.recv().await {
            match message {
                ActorMessage::TypedAction(typed_msg, response_tx) => {
                    // Process the action - reaction is already serialized inside act()
                    let result = self.actor.act(typed_msg.action, &self.actors, &self.memories).await;

                    // Simply forward the serialized reaction bytes (zero-copy!)
                    let _ = response_tx.send(result);
                }
                ActorMessage::Shutdown => {
                    tracing::info!("Actor {} shutting down", self.id.as_str());
                    break;
                }

            }
        }

        tracing::info!("Actor {} stopped", self.id.as_str());
        Ok(())
    }
}

/// Actor Handle - the external interface following Alice Ryhl's pattern
#[derive(Clone)]
pub struct ActorHandle<T: Actor> {
    pub id: ActorId,
    pub sender: mpsc::Sender<ActorMessage<T::Action>>,
}

impl<T: Actor> ActorHandle<T> {
    pub fn new(
        id: ActorId,
        sender: mpsc::Sender<ActorMessage<T::Action>>,
    ) -> Self {
        Self { id, sender }
    }

    /// Send an action without waiting for response (tell pattern) - fully type-safe
    pub async fn tell(&self, action: T::Action) -> TamtilResult<()> {
        let typed_msg = TypedMessage {
            action,
            request_id: None,
            sender_id: self.id.clone(),
        };

        let (response_tx, _response_rx) = oneshot::channel();

        self.sender
            .send(ActorMessage::TypedAction(typed_msg, response_tx))
            .await
            .map_err(|_| TamtilError::Communication {
                message: format!("Failed to send message to actor {}", self.id.as_str())
            })?;

        Ok(())
    }

    /// Send an action and wait for reaction (ask pattern) - zero-copy access
    /// Returns the serialized reaction bytes for zero-copy access via rkyv
    pub async fn ask(&self, action: T::Action) -> TamtilResult<Vec<u8>> {
        let typed_msg = TypedMessage {
            action,
            request_id: Some(1), // TODO: Use proper request ID generation
            sender_id: self.id.clone(),
        };

        let (response_tx, response_rx) = oneshot::channel();

        self.sender
            .send(ActorMessage::TypedAction(typed_msg, response_tx))
            .await
            .map_err(|_| TamtilError::Communication {
                message: format!("Failed to send message to actor {}", self.id.as_str())
            })?;

        match response_rx.await {
            Ok(reaction_bytes) => reaction_bytes,
            Err(_) => Err(TamtilError::Communication {
                message: format!("Actor {} did not respond", self.id.as_str())
            }),
        }
    }

    /// Access the reaction data via zero-copy rkyv (developers use this for type-safe access)
    pub fn access_reaction<R: Archive>(reaction_bytes: &[u8]) -> TamtilResult<&R::Archived> {
        use rkyv::api::access_unchecked;
        // SAFETY: In production, we'd validate this data first
        // For now, we trust that the actor serialized it correctly
        unsafe {
            Ok(access_unchecked::<R::Archived>(reaction_bytes))
        }
    }

    /// Shutdown the actor gracefully
    pub async fn shutdown(&self) -> TamtilResult<()> {
        self.sender
            .send(ActorMessage::Shutdown)
            .await
            .map_err(|_| TamtilError::Communication {
                message: format!("Failed to send shutdown to actor {}", self.id.as_str())
            })?;
        Ok(())
    }
}

// Implement ErasedActorHandle for ActorHandle
#[async_trait]
impl<T: Actor> ErasedActorHandle for ActorHandle<T> {
    fn actor_id(&self) -> &ActorId {
        &self.id
    }

    async fn shutdown(&self) -> TamtilResult<()> {
        ActorHandle::shutdown(self).await
    }
}

/// Builder pattern for creating actors with integrated memories and actions
/// This shifts the usage from letting developers create actors to offering pre-implemented actors
pub struct ActorBuilder {
    id: ActorId,
    memories: ActorMemories,
}

impl ActorBuilder {
    /// Create a new actor builder with integrated memories
    pub async fn new(id: impl Into<ActorId>) -> TamtilResult<Self> {
        Ok(Self {
            id: id.into(),
            memories: ActorMemories::new().await?,
        })
    }

    /// Get a reference to the actor's memories for configuration
    pub fn memories(&self) -> &ActorMemories {
        &self.memories
    }

    /// Get a mutable reference to the actor's memories for configuration
    pub fn memories_mut(&mut self) -> &mut ActorMemories {
        &mut self.memories
    }

    /// Pre-populate the actor's memories with initial data
    pub async fn with_initial_memory(self, key: &str, value: MemoryValue) -> TamtilResult<Self> {
        let operations = vec![MemoryOperation::Create {
            key: key.to_string(),
            value,
        }];
        self.memories.remember_operations(operations).await?;
        Ok(self)
    }

    /// Connect this actor's memories to another memory graph
    pub async fn connect_to(self, from_memory: &str, to_memory: &str, relation: &str) -> TamtilResult<Self> {
        let operations = vec![MemoryOperation::Link {
            from: from_memory.to_string(),
            to: to_memory.to_string(),
            relation: relation.to_string(),
        }];
        self.memories.remember_operations(operations).await?;
        Ok(self)
    }

    /// Build a concrete actor implementation that can handle the specified action type
    /// This is where developers extend the actor with their business logic
    pub fn build<A: Action + fmt::Debug>(self) -> BuiltActor<A> {
        BuiltActor {
            id: self.id,
            memories: self.memories,
            _phantom: std::marker::PhantomData,
        }
    }
}

/// A concrete actor implementation built with the builder pattern
/// This actor has integrated memories and can handle actions of type A
pub struct BuiltActor<A: Action + fmt::Debug> {
    id: ActorId,
    memories: ActorMemories,
    _phantom: std::marker::PhantomData<A>,
}

#[async_trait]
impl<A: Action + fmt::Debug> Actor for BuiltActor<A> {
    type Action = A;

    async fn act(&mut self, action: Self::Action, actors: &Actors, _memories: &ActorMemories) -> TamtilResult<Vec<u8>> {
        // Use the integrated memories instead of the passed-in memories
        // The action is responsible for remembering reactions using the memories API
        let reaction = action.act(actors, &self.memories).await?;

        // Serialize the reaction using rkyv - the reaction is returned but not automatically remembered
        let reaction_bytes = rkyv::api::high::to_bytes::<RancorError>(&reaction)
            .map_err(|e| TamtilError::Serialization {
                message: format!("Failed to serialize reaction: {}", e)
            })?;

        Ok(reaction_bytes.to_vec())
    }
}

impl<A: Action + fmt::Debug> BuiltActor<A> {
    /// Get the actor's ID
    pub fn id(&self) -> &ActorId {
        &self.id
    }

    /// Get access to the actor's integrated memories
    pub fn memories(&self) -> &ActorMemories {
        &self.memories
    }

    /// Recall a value from the actor's memories with zero-copy access
    pub async fn recall<T>(&self, key: &str) -> Option<T>
    where
        T: Archive,
        T::Archived: for<'a> rkyv::bytecheck::CheckBytes<rkyv::api::high::HighValidator<'a, rkyv::rancor::Error>>
            + rkyv::Deserialize<T, rkyv::rancor::Strategy<rkyv::de::pooling::Pool, rkyv::rancor::Error>>
    {
        self.memories.recall(key).await
    }

    /// Remember a reaction in the actor's memories
    pub async fn remember_reaction<R>(&self, reaction: &R) -> TamtilResult<()>
    where
        R: Reaction + for<'a> rkyv::Serialize<rkyv::api::high::HighSerializer<rkyv::util::AlignedVec, rkyv::ser::allocator::ArenaHandle<'a>, rkyv::rancor::Error>>
    {
        self.memories.remember_reaction(reaction).await
    }

    /// Remember multiple reactions atomically in the actor's memories
    pub async fn remember_reactions<R: Reaction>(&self, reactions: &[R]) -> TamtilResult<()> {
        self.memories.remember_reactions(reactions).await
    }

    /// Remember operations in the actor's memories (lower level API)
    pub async fn remember_operations(&self, operations: Vec<MemoryOperation>) -> TamtilResult<()> {
        self.memories.remember_operations(operations).await
    }

    /// Query the actor's memory graph using BFS
    pub async fn query_graph(&self, start_memory_id: &str, max_depth: usize) -> Vec<TraversalResult> {
        self.memories.query_graph_bfs(start_memory_id, max_depth).await
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    // All types are now in this module

    /// Test the new integrated actor builder pattern
    #[tokio::test]
    async fn test_actor_builder_pattern() {
        // Create an actor using the builder pattern
        let builder = ActorBuilder::new("test_actor").await.unwrap();

        // Pre-populate with some initial data
        let builder = builder
            .with_initial_memory("config:name", MemoryValue::new(&"TestActor".to_string()).unwrap())
            .await
            .unwrap();

        let builder = builder
            .with_initial_memory("config:version", MemoryValue::new(&"1.0.0".to_string()).unwrap())
            .await
            .unwrap();

        // Connect to another memory graph
        let builder = builder
            .connect_to("config", "system", "belongs_to")
            .await
            .unwrap();

        // Build the actor (we'll use a dummy action type for testing)
        #[derive(Debug, Clone, Archive, rkyv::Serialize, rkyv::Deserialize)]
        #[rkyv(derive(Debug))]
        struct TestAction {
            message: String,
        }

        #[derive(Debug, Clone, Archive, rkyv::Serialize, rkyv::Deserialize)]
        #[rkyv(derive(Debug))]
        struct TestReaction {
            response: String,
        }

        impl Reaction for TestReaction {
            fn remember(&self) -> Vec<MemoryOperation> {
                vec![MemoryOperation::Create {
                    key: "reactions:last_response".to_string(),
                    value: MemoryValue::new(&self.response).unwrap(),
                }]
            }
        }

        impl Action for TestAction {
            type Reaction = TestReaction;

            async fn act(&self, _actors: &Actors, memories: &ActorMemories) -> TamtilResult<Self::Reaction> {
                // Access the actor's memories
                let name: Option<String> = memories.recall("config:name").await;
                let version: Option<String> = memories.recall("config:version").await;

                let response = format!(
                    "Hello from {} v{}, received: {}",
                    name.unwrap_or_else(|| "Unknown".to_string()),
                    version.unwrap_or_else(|| "0.0.0".to_string()),
                    self.message
                );

                let reaction = TestReaction { response };

                // Developer controls when to remember the reaction
                // This could be done at different points in the action logic
                memories.remember_reaction(&reaction).await?;

                // Could also remember multiple reactions during processing
                let progress_reaction = TestReaction {
                    response: "Processing completed".to_string()
                };
                memories.remember_reaction(&progress_reaction).await?;

                Ok(reaction)
            }
        }

        let actor: BuiltActor<TestAction> = builder.build();

        // Test that we can access the pre-populated memories
        let name: Option<String> = actor.recall("config:name").await;
        assert_eq!(name, Some("TestActor".to_string()));

        let version: Option<String> = actor.recall("config:version").await;
        assert_eq!(version, Some("1.0.0".to_string()));

        // Test graph connectivity
        let connected = actor.memories().get_connected("config").await;
        assert!(connected.contains(&"system".to_string()));
    }

    /// Test the integrated memories system with graph operations
    #[tokio::test]
    async fn test_integrated_memories_graph() {
        let memories = ActorMemories::new().await.unwrap();

        // Create a graph: users -> posts -> comments
        let operations = vec![
            MemoryOperation::Create {
                key: "users:alice:name".to_string(),
                value: MemoryValue::new(&"Alice".to_string()).unwrap(),
            },
            MemoryOperation::Create {
                key: "posts:post1:title".to_string(),
                value: MemoryValue::new(&"Hello World".to_string()).unwrap(),
            },
            MemoryOperation::Create {
                key: "comments:c1:text".to_string(),
                value: MemoryValue::new(&"Great post!".to_string()).unwrap(),
            },
            MemoryOperation::Link {
                from: "users".to_string(),
                to: "posts".to_string(),
                relation: "authored".to_string(),
            },
            MemoryOperation::Link {
                from: "posts".to_string(),
                to: "comments".to_string(),
                relation: "has_comment".to_string(),
            },
        ];

        memories.remember(operations).await.unwrap();

        // Test BFS graph traversal
        let results = memories.query_graph_bfs("users", 2).await;

        // Should find users, posts, and comments
        let memory_ids: Vec<String> = results.iter().map(|r| r.memory_id.clone()).collect();
        assert!(memory_ids.contains(&"users".to_string()));
        assert!(memory_ids.contains(&"posts".to_string()));
        assert!(memory_ids.contains(&"comments".to_string()));

        // Test recall functionality
        let alice_name: Option<String> = memories.recall("users:alice:name").await;
        assert_eq!(alice_name, Some("Alice".to_string()));

        let post_title: Option<String> = memories.recall("posts:post1:title").await;
        assert_eq!(post_title, Some("Hello World".to_string()));
    }

    /// Test atomic memory operations
    #[tokio::test]
    async fn test_atomic_memory_operations() {
        let memories = ActorMemories::new().await.unwrap();

        // Test atomic transaction
        let transaction_ops = vec![
            MemoryOperation::Create {
                key: "account:1:balance".to_string(),
                value: MemoryValue::new(&100.0).unwrap(),
            },
            MemoryOperation::Create {
                key: "transaction:tx1:amount".to_string(),
                value: MemoryValue::new(&50.0).unwrap(),
            },
            MemoryOperation::Update {
                key: "account:1:balance".to_string(),
                value: MemoryValue::new(&50.0).unwrap(),
            },
        ];

        memories.remember(transaction_ops).await.unwrap();

        // Verify all operations succeeded atomically
        let balance: Option<f64> = memories.recall("account:1:balance").await;
        assert_eq!(balance, Some(50.0));

        let amount: Option<f64> = memories.recall("transaction:tx1:amount").await;
        assert_eq!(amount, Some(50.0));
    }

    /// Test developer-controlled reaction remembering
    #[tokio::test]
    async fn test_developer_controlled_reaction_remembering() {
        let memories = ActorMemories::new().await.unwrap();

        // Define test reactions
        #[derive(Debug, Clone, Archive, rkyv::Serialize, rkyv::Deserialize)]
        #[rkyv(derive(Debug))]
        struct OrderCreated {
            order_id: String,
            amount: f64,
        }

        #[derive(Debug, Clone, Archive, rkyv::Serialize, rkyv::Deserialize)]
        #[rkyv(derive(Debug))]
        struct PaymentProcessed {
            payment_id: String,
            order_id: String,
            amount: f64,
        }

        impl Reaction for OrderCreated {
            fn remember(&self) -> Vec<MemoryOperation> {
                vec![
                    MemoryOperation::Create {
                        key: format!("orders:{}:amount", self.order_id),
                        value: MemoryValue::new(&self.amount).unwrap(),
                    },
                    MemoryOperation::Create {
                        key: format!("orders:{}:status", self.order_id),
                        value: MemoryValue::new(&"created".to_string()).unwrap(),
                    },
                ]
            }
        }

        impl Reaction for PaymentProcessed {
            fn remember(&self) -> Vec<MemoryOperation> {
                vec![
                    MemoryOperation::Create {
                        key: format!("payments:{}:amount", self.payment_id),
                        value: MemoryValue::new(&self.amount).unwrap(),
                    },
                    MemoryOperation::Update {
                        key: format!("orders:{}:status", self.order_id),
                        value: MemoryValue::new(&"paid".to_string()).unwrap(),
                    },
                    MemoryOperation::Link {
                        from: "orders".to_string(),
                        to: "payments".to_string(),
                        relation: "paid_with".to_string(),
                    },
                ]
            }
        }

        // Test remembering a single reaction
        let order_reaction = OrderCreated {
            order_id: "order_123".to_string(),
            amount: 99.99,
        };
        memories.remember_reaction(&order_reaction).await.unwrap();

        // Test remembering multiple reactions atomically
        let payment_reaction = PaymentProcessed {
            payment_id: "payment_456".to_string(),
            order_id: "order_123".to_string(),
            amount: 99.99,
        };

        let completion_reaction = PaymentProcessed {
            payment_id: "payment_789".to_string(),
            order_id: "order_124".to_string(),
            amount: 149.99,
        };

        // Remember reactions individually since they are different types
        memories.remember_reaction(&payment_reaction).await.unwrap();
        memories.remember_reaction(&completion_reaction).await.unwrap();

        // Verify all reactions were remembered correctly
        let order_amount: Option<f64> = memories.recall("orders:order_123:amount").await;
        assert_eq!(order_amount, Some(99.99));

        let order_status: Option<String> = memories.recall("orders:order_123:status").await;
        assert_eq!(order_status, Some("paid".to_string()));

        let payment_amount: Option<f64> = memories.recall("payments:payment_456:amount").await;
        assert_eq!(payment_amount, Some(99.99));

        let new_order_amount: Option<f64> = memories.recall("orders:order_124:amount").await;
        assert_eq!(new_order_amount, Some(149.99));

        // Verify graph connections
        let connected = memories.get_connected("orders").await;
        assert!(connected.contains(&"payments".to_string()));
    }

    /// Test fault-tolerant event sourcing with reaction log
    #[tokio::test]
    async fn test_fault_tolerant_event_sourcing() {
        let memories = ActorMemories::new().await.unwrap();

        // Define a test reaction
        #[derive(Debug, Clone, Archive, rkyv::Serialize, rkyv::Deserialize)]
        #[rkyv(derive(Debug))]
        struct BankTransfer {
            from_account: String,
            to_account: String,
            amount: f64,
            transaction_id: String,
        }

        impl Reaction for BankTransfer {
            fn remember(&self) -> Vec<MemoryOperation> {
                vec![
                    // Debit from account
                    MemoryOperation::Create {
                        key: format!("transactions:{}:from", self.transaction_id),
                        value: MemoryValue::new(&self.from_account).unwrap(),
                    },
                    MemoryOperation::Create {
                        key: format!("transactions:{}:to", self.transaction_id),
                        value: MemoryValue::new(&self.to_account).unwrap(),
                    },
                    MemoryOperation::Create {
                        key: format!("transactions:{}:amount", self.transaction_id),
                        value: MemoryValue::new(&self.amount).unwrap(),
                    },
                    // Link accounts to transaction
                    MemoryOperation::Link {
                        from: "accounts".to_string(),
                        to: "transactions".to_string(),
                        relation: "has_transaction".to_string(),
                    },
                ]
            }
        }

        // Create some bank transfer reactions
        let transfer1 = BankTransfer {
            from_account: "acc_123".to_string(),
            to_account: "acc_456".to_string(),
            amount: 100.0,
            transaction_id: "tx_001".to_string(),
        };

        let transfer2 = BankTransfer {
            from_account: "acc_456".to_string(),
            to_account: "acc_789".to_string(),
            amount: 50.0,
            transaction_id: "tx_002".to_string(),
        };

        // Remember reactions (they get stored in the reaction log)
        memories.remember_reaction(&transfer1).await.unwrap();
        memories.remember_reaction(&transfer2).await.unwrap();

        // Verify the reactions are in the log
        let decided_idx = memories.reaction_log().get_decided_idx().await;
        assert_eq!(decided_idx, 2);

        // Verify the projected state
        let from_account: Option<String> = memories.recall("transactions:tx_001:from").await;
        assert_eq!(from_account, Some("acc_123".to_string()));

        let amount: Option<f64> = memories.recall("transactions:tx_002:amount").await;
        assert_eq!(amount, Some(50.0));

        // Test recovery by creating a new memories instance and replaying
        let recovered_memories = ActorMemories::new().await.unwrap();

        // In a real scenario, we'd copy the reaction log from the original instance
        // For this test, we'll simulate recovery by manually applying the same reactions
        recovered_memories.remember_reaction(&transfer1).await.unwrap();
        recovered_memories.remember_reaction(&transfer2).await.unwrap();

        // Verify recovered state matches original
        let recovered_from: Option<String> = recovered_memories.recall("transactions:tx_001:from").await;
        assert_eq!(recovered_from, Some("acc_123".to_string()));

        let recovered_amount: Option<f64> = recovered_memories.recall("transactions:tx_002:amount").await;
        assert_eq!(recovered_amount, Some(50.0));

        // Test graph algorithms on the recovered state
        let shortest_paths = GraphAlgorithms::dijkstra_shortest_path(
            &recovered_memories,
            "accounts",
            Some("transactions"),
            5.0
        ).await;

        assert!(shortest_paths.contains_key("transactions"));
        let (distance, path) = &shortest_paths["transactions"];
        assert_eq!(*distance, 1.0);
        assert_eq!(path, &vec!["accounts".to_string(), "transactions".to_string()]);
    }

    /// Test compaction of reaction log (inspired by OmniPaxos)
    #[tokio::test]
    async fn test_reaction_log_compaction() {
        let memories = ActorMemories::new().await.unwrap();

        // Create multiple reactions
        #[derive(Debug, Clone, Archive, rkyv::Serialize, rkyv::Deserialize)]
        #[rkyv(derive(Debug))]
        struct SimpleReaction {
            id: String,
            value: i32,
        }

        impl Reaction for SimpleReaction {
            fn remember(&self) -> Vec<MemoryOperation> {
                vec![MemoryOperation::Create {
                    key: format!("simple:{}", self.id),
                    value: MemoryValue::new(&self.value).unwrap(),
                }]
            }
        }

        // Add several reactions
        for i in 0..10 {
            let reaction = SimpleReaction {
                id: format!("item_{}", i),
                value: i * 10,
            };
            memories.remember_reaction(&reaction).await.unwrap();
        }

        // Verify all reactions are in the log
        let decided_idx = memories.reaction_log().get_decided_idx().await;
        assert_eq!(decided_idx, 10);

        // Create a snapshot of the current state
        let snapshot_data = b"snapshot_of_state_at_index_5".to_vec();
        memories.reaction_log().compact(5, snapshot_data.clone()).await.unwrap();

        // Verify compaction
        let compacted_idx = memories.reaction_log().get_compacted_idx().await;
        assert_eq!(compacted_idx, 5);

        let snapshot = memories.reaction_log().get_snapshot().await;
        assert_eq!(snapshot, Some(snapshot_data));

        // Verify we can still access recent reactions
        let recent_reactions = memories.reaction_log().get_reactions_from(5).await;
        assert_eq!(recent_reactions.len(), 5); // reactions 6-10
    }
}
