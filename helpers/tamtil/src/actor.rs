//! Actor module - <PERSON>'s pattern with integrated memories
//!
//! This module implements the core actor system following <PERSON>'s pattern:
//! - Separate Task and Handle structs
//! - Oneshot channels for responses
//! - tokio::spawn in handle constructor
//! - Zero-copy serialization with rkyv
//! - Integrated graph-based memories with petgraph-inspired algorithms

// All core types are now defined in this module
use async_trait::async_trait;
use rkyv::{Archive, Serialize, Deserialize, api::high::{to_bytes, from_bytes}, rancor::Error as RancorError};
use std::{collections::{HashMap, VecDeque, HashSet}, sync::Arc, fmt};
use tokio::sync::{mpsc, oneshot, RwLock};

/// TAMTIL Error types
#[derive(Debug, thiserror::Error)]
pub enum TamtilError {
    #[error("Serialization error: {message}")]
    Serialization { message: String },

    #[error("Deserialization error: {message}")]
    Deserialization { message: String },

    #[error("Actor not found: {id}")]
    ActorNotFound { id: String },

    #[error("Communication error: {message}")]
    Communication { message: String },

    #[error("Invalid operation: {message}")]
    InvalidOperation { message: String },

    #[error("Consensus failure: {message}")]
    ConsensusFailure { message: String },
}

/// Result type for TAMTIL operations
pub type TamtilResult<T> = Result<T, TamtilError>;

/// Actor ID for hierarchical addressing
#[derive(Debug, Clone, Hash, Archive, Serialize, Deserialize)]
#[rkyv(derive(Debug))]
pub struct ActorId(pub String);

impl PartialEq for ActorId {
    fn eq(&self, other: &Self) -> bool {
        self.0 == other.0
    }
}

impl Eq for ActorId {}

impl ActorId {
    pub fn new(id: impl Into<String>) -> Self {
        Self(id.into())
    }

    pub fn as_str(&self) -> &str {
        &self.0
    }

    /// Get parent actor ID
    pub fn parent(&self) -> Option<Self> {
        self.0.rfind('/').map(|pos| Self(self.0[..pos].to_string()))
    }

    /// Get the depth in the hierarchy (0 = root)
    pub fn depth(&self) -> usize {
        if self.0.is_empty() { 0 } else { self.0.matches('/').count() }
    }

    /// Check if this is a child of another actor
    pub fn is_child_of(&self, parent: &ActorId) -> bool {
        self.0.starts_with(parent.as_str())
            && self.0.len() > parent.0.len()
            && self.0.chars().nth(parent.0.len()) == Some('/')
    }
}

/// Zero-copy memory value leveraging rkyv's archived types for maximum performance
#[derive(Debug, Clone, PartialEq, Archive, Serialize, Deserialize)]
#[rkyv(derive(Debug))]
pub struct MemoryValue {
    /// Raw rkyv serialized bytes - optimized for zero-copy access
    pub bytes: Vec<u8>,
}

impl MemoryValue {
    /// Create MemoryValue from any rkyv-serializable type using high-level API
    pub fn new<T>(value: &T) -> TamtilResult<Self>
    where
        T: Archive + for<'a> rkyv::Serialize<rkyv::api::high::HighSerializer<rkyv::util::AlignedVec, rkyv::ser::allocator::ArenaHandle<'a>, rkyv::rancor::Error>>
    {
        let bytes = rkyv::api::high::to_bytes::<rkyv::rancor::Error>(value)
            .map_err(|e| TamtilError::Serialization {
                message: format!("Failed to serialize to MemoryValue: {}", e)
            })?;
        Ok(Self { bytes: bytes.to_vec() })
    }

    /// Zero-copy access to archived value - most performant option
    /// Note: This requires the type to implement rkyv::Portable + CheckBytes
    pub fn archived<T>(&self) -> TamtilResult<&T>
    where
        T: rkyv::Portable + for<'a> rkyv::bytecheck::CheckBytes<rkyv::api::high::HighValidator<'a, rkyv::rancor::Error>>,
    {
        rkyv::api::high::access::<T, rkyv::rancor::Error>(&self.bytes)
            .map_err(|e| TamtilError::Deserialization {
                message: format!("Failed to access archived value: {}", e)
            })
    }

    /// Get value with deserialization (when zero-copy isn't possible)
    pub fn get<T>(&self) -> TamtilResult<T>
    where
        T: Archive,
        T::Archived: for<'a> rkyv::bytecheck::CheckBytes<rkyv::api::high::HighValidator<'a, rkyv::rancor::Error>>
            + rkyv::Deserialize<T, rkyv::rancor::Strategy<rkyv::de::pooling::Pool, rkyv::rancor::Error>>
    {
        rkyv::api::high::from_bytes::<T, rkyv::rancor::Error>(&self.bytes)
            .map_err(|e| TamtilError::Deserialization {
                message: format!("Failed to deserialize memory value: {}", e)
            })
    }

    /// Convenience constructors optimized for common types
    pub fn string(s: &str) -> TamtilResult<Self> {
        Self::new(&s.to_string())
    }

    pub fn number(n: f64) -> TamtilResult<Self> {
        Self::new(&n)
    }

    pub fn boolean(b: bool) -> TamtilResult<Self> {
        Self::new(&b)
    }

    /// Raw bytes access for maximum flexibility
    pub fn raw(&self) -> &[u8] {
        &self.bytes
    }
}

/// Memory operations for atomic state changes
#[derive(Debug, Clone, Archive, Serialize, Deserialize)]
#[rkyv(derive(Debug))]
pub enum MemoryOperation {
    Create { key: String, value: MemoryValue },
    Update { key: String, value: MemoryValue },
    Delete { key: String },
    Link { from: String, to: String, relation: String },
    Unlink { from: String, to: String, relation: String },
}

/// Action trait - represents operations that can be performed
pub trait Action: Send + Sync + 'static {
    type Reaction: Reaction;

    fn act(
        &self,
        actors: &Actors,
        memories: &ActorMemories,
    ) -> impl std::future::Future<Output = TamtilResult<Self::Reaction>> + Send
    where
        Self: Send;
}

/// The Reaction trait is central to TAMTIL's design.
/// Every actor action produces a reaction that can be remembered.
pub trait Reaction: Archive + Send + Sync + 'static + for<'a> rkyv::Serialize<rkyv::api::high::HighSerializer<rkyv::util::AlignedVec, rkyv::ser::allocator::ArenaHandle<'a>, rkyv::rancor::Error>> {
    /// Convert this reaction into memory operations for persistence
    fn remember(&self) -> Vec<MemoryOperation>;
}

/// Actor system for managing multiple actors
#[derive(Clone)]
pub struct Actors {
    /// Store actor memories by ID for state persistence - this is the key to rkyv approach
    memories: Arc<RwLock<HashMap<ActorId, ActorMemories>>>,
}

impl Actors {
    pub async fn new() -> TamtilResult<Self> {
        Ok(Self {
            memories: Arc::new(RwLock::new(HashMap::new())),
        })
    }

    pub async fn spawn<T: Actor>(&self, id: ActorId, actor: T) -> TamtilResult<()> {
        let (_sender, receiver) = mpsc::channel(100);
        let memories = ActorMemories::new().await?;

        // Store memories for this actor - this is the key insight
        {
            let mut actor_memories = self.memories.write().await;
            actor_memories.insert(id.clone(), memories.clone());
        }

        let task = ActorTask::new(id.clone(), actor, receiver, self.clone(), memories);
        tokio::spawn(async move {
            let _ = task.run().await;
        });

        Ok(())
    }

    pub fn actor(&self, id: &ActorId) -> Proxy {
        Proxy {
            id: id.clone(),
            memories: self.memories.clone(),
        }
    }
}

/// Proxy for accessing actors - leverages rkyv type safety
pub struct Proxy {
    id: ActorId,
    memories: Arc<RwLock<HashMap<ActorId, ActorMemories>>>,
}

impl Proxy {
    pub async fn act<A>(&self, action: A) -> TamtilResult<Vec<u8>>
    where
        A: Action + fmt::Debug + Archive + for<'a> rkyv::Serialize<rkyv::api::high::HighSerializer<rkyv::util::AlignedVec, rkyv::ser::allocator::ArenaHandle<'a>, rkyv::rancor::Error>>
    {
        // Get the actor's persistent memories
        let memories = {
            let actor_memories = self.memories.read().await;
            actor_memories.get(&self.id).cloned()
        };

        let memories = match memories {
            Some(mem) => mem,
            None => return Err(TamtilError::ActorNotFound {
                id: self.id.as_str().to_string()
            })
        };

        // Execute the action with the actor's persistent memories
        let actors = Actors::new().await?; // Create a temporary actors reference
        let reaction = action.act(&actors, &memories).await?;

        // Use memories to remember and serialize the reaction (this applies the memory operations!)
        let reaction_bytes = memories.remember_and_serialize(&reaction).await?;

        Ok(reaction_bytes)
    }
}

/// Trait for type-erased actor handles
#[async_trait]
pub trait ErasedActorHandle: Send + Sync {
    fn actor_id(&self) -> &ActorId;
    async fn shutdown(&self) -> TamtilResult<()>;
}

/// Fault-tolerant storage for reactions (event sourcing)
/// Inspired by OmniPaxos storage patterns
#[derive(Clone)]
pub struct ReactionLog {
    /// Sequence of reactions (the single source of truth)
    reactions: Arc<RwLock<Vec<Vec<u8>>>>, // serialized reactions
    /// Current decided index (reactions up to this index are committed)
    decided_idx: Arc<RwLock<usize>>,
    /// Snapshot of projected state at compacted_idx
    snapshot: Arc<RwLock<Option<Vec<u8>>>>,
    /// Index up to which reactions have been compacted into snapshot
    compacted_idx: Arc<RwLock<usize>>,
}

impl ReactionLog {
    /// Create a new reaction log
    pub fn new() -> Self {
        Self {
            reactions: Arc::new(RwLock::new(Vec::new())),
            decided_idx: Arc::new(RwLock::new(0)),
            snapshot: Arc::new(RwLock::new(None)),
            compacted_idx: Arc::new(RwLock::new(0)),
        }
    }

    /// Append a reaction to the log (atomic operation)
    pub async fn append(&self, reaction_bytes: Vec<u8>) -> TamtilResult<usize> {
        let mut reactions = self.reactions.write().await;
        reactions.push(reaction_bytes);
        let new_idx = reactions.len();

        // Auto-commit for single node (in distributed mode, this would be consensus-driven)
        let mut decided = self.decided_idx.write().await;
        *decided = new_idx;

        tracing::debug!("Appended reaction at index {}", new_idx);
        Ok(new_idx)
    }

    /// Get reactions from a specific index (for replay/recovery)
    pub async fn get_reactions_from(&self, from_idx: usize) -> Vec<Vec<u8>> {
        let reactions = self.reactions.read().await;
        let decided = *self.decided_idx.read().await;

        if from_idx >= reactions.len() {
            return Vec::new();
        }

        // Only return decided reactions
        let end_idx = std::cmp::min(decided, reactions.len());
        reactions[from_idx..end_idx].to_vec()
    }

    /// Get the current decided index
    pub async fn decided(&self) -> usize {
        *self.decided_idx.read().await
    }

    /// Compact the log by creating a snapshot (inspired by OmniPaxos compaction)
    pub async fn compact(&self, up_to_idx: usize, snapshot_data: Vec<u8>) -> TamtilResult<()> {
        let decided = *self.decided_idx.read().await;

        if up_to_idx > decided {
            return Err(TamtilError::InvalidOperation {
                message: format!("Cannot compact beyond decided index {} (requested: {})", decided, up_to_idx)
            });
        }

        // Store snapshot
        let mut snapshot = self.snapshot.write().await;
        *snapshot = Some(snapshot_data);

        // Update compacted index
        let mut compacted = self.compacted_idx.write().await;
        *compacted = up_to_idx;

        // Remove compacted reactions
        let mut reactions = self.reactions.write().await;
        if up_to_idx > 0 && up_to_idx <= reactions.len() {
            reactions.drain(0..up_to_idx);
        }

        tracing::info!("Compacted reaction log up to index {}", up_to_idx);
        Ok(())
    }

    /// Get the current snapshot
    pub async fn snapshot(&self) -> Option<Vec<u8>> {
        self.snapshot.read().await.clone()
    }

    /// Get the compacted index
    pub async fn compacted(&self) -> usize {
        *self.compacted_idx.read().await
    }
}

/// Graph algorithms adapted from petgraph for TAMTIL memories
pub struct GraphAlgorithms;

impl GraphAlgorithms {
    /// Dijkstra's shortest path algorithm adapted for memory graphs
    pub async fn dijkstra_shortest_path(
        memories: &ActorMemories,
        start: &str,
        goal: Option<&str>,
        max_distance: f64,
    ) -> HashMap<String, (f64, Vec<String>)> {
        let mut distances = HashMap::new();
        let mut paths = HashMap::new();
        let mut visited = HashSet::new();
        let mut heap = std::collections::BinaryHeap::new();

        // Start with the initial node
        distances.insert(start.to_string(), 0.0);
        paths.insert(start.to_string(), vec![start.to_string()]);
        heap.push(std::cmp::Reverse((0.0 as i64, start.to_string())));

        while let Some(std::cmp::Reverse((dist_times_1000, current))) = heap.pop() {
            let current_dist = dist_times_1000 as f64 / 1000.0;

            if visited.contains(&current) {
                continue;
            }

            if let Some(goal_node) = goal {
                if current == goal_node {
                    break;
                }
            }

            if current_dist > max_distance {
                continue;
            }

            visited.insert(current.clone());

            // Get connected memories
            let connected = memories.connected(&current).await;
            for neighbor in connected {
                if visited.contains(&neighbor) {
                    continue;
                }

                let edge_weight = 1.0; // Default weight, could be customized
                let new_dist = current_dist + edge_weight;

                if new_dist <= max_distance {
                    let should_update = match distances.get(&neighbor) {
                        Some(&existing_dist) => new_dist < existing_dist,
                        None => true,
                    };

                    if should_update {
                        distances.insert(neighbor.clone(), new_dist);

                        let mut new_path = paths.get(&current).unwrap().clone();
                        new_path.push(neighbor.clone());
                        paths.insert(neighbor.clone(), new_path);

                        heap.push(std::cmp::Reverse(((new_dist * 1000.0) as i64, neighbor)));
                    }
                }
            }
        }

        // Combine distances and paths
        let mut result = HashMap::new();
        for (node, dist) in distances {
            if let Some(path) = paths.get(&node) {
                result.insert(node, (dist, path.clone()));
            }
        }

        result
    }

    /// Connected components algorithm adapted from petgraph
    pub async fn connected_components(memories: &ActorMemories, start_nodes: &[String]) -> Vec<Vec<String>> {
        let mut visited = HashSet::new();
        let mut components = Vec::new();

        for start_node in start_nodes {
            if visited.contains(start_node) {
                continue;
            }

            let mut component = Vec::new();
            let mut stack = vec![start_node.clone()];

            while let Some(current) = stack.pop() {
                if visited.contains(&current) {
                    continue;
                }

                visited.insert(current.clone());
                component.push(current.clone());

                // Add all connected nodes to the stack
                let connected = memories.connected(&current).await;
                for neighbor in connected {
                    if !visited.contains(&neighbor) {
                        stack.push(neighbor);
                    }
                }
            }

            if !component.is_empty() {
                components.push(component);
            }
        }

        components
    }
}

/// Direction for graph traversal (adapted from petgraph)
#[derive(Debug, Clone, Copy, PartialEq, Eq, Archive, rkyv::Serialize, rkyv::Deserialize)]
#[rkyv(derive(Debug))]
pub enum Direction {
    /// Outgoing edges from a memory
    Outgoing,
    /// Incoming edges to a memory
    Incoming,
}

impl Direction {
    /// Return the opposite direction
    pub fn opposite(self) -> Direction {
        match self {
            Direction::Outgoing => Direction::Incoming,
            Direction::Incoming => Direction::Outgoing,
        }
    }
}

/// Edge weight for memory connections
#[derive(Debug, Clone, Archive, rkyv::Serialize, rkyv::Deserialize)]
#[rkyv(derive(Debug))]
pub struct EdgeWeight {
    pub relation: String,
    pub weight: f64,
    pub metadata: HashMap<String, MemoryValue>,
}

impl EdgeWeight {
    pub fn new(relation: String) -> Self {
        Self {
            relation,
            weight: 1.0,
            metadata: HashMap::new(),
        }
    }

    pub fn with_weight(mut self, weight: f64) -> Self {
        self.weight = weight;
        self
    }

    pub fn with_metadata(mut self, key: String, value: MemoryValue) -> Self {
        self.metadata.insert(key, value);
        self
    }
}

/// Path finding algorithms (inspired by petgraph)
#[derive(Debug, Clone)]
pub enum PathAlgorithm {
    /// Breadth-first search
    BFS,
    /// Depth-first search
    DFS,
    /// Simple traversal (current implementation)
    Simple,
}

/// Graph traversal result
#[derive(Debug, Clone)]
pub struct TraversalResult {
    pub memory_id: String,
    pub distance: usize,
    pub path: Vec<String>,
    pub total_weight: f64,
}

/// A Memory is a collection of key-values under one ID
#[derive(Debug, Clone, Archive, rkyv::Serialize, rkyv::Deserialize)]
#[rkyv(derive(Debug))]
pub struct Memory {
    pub id: String,
    pub data: HashMap<String, MemoryValue>,
    pub connections: Vec<String>, // IDs of connected memories
}

impl Memory {
    pub fn new(id: String) -> Self {
        Self {
            id,
            data: HashMap::new(),
            connections: Vec::new(),
        }
    }

    pub fn get(&self, key: &str) -> Option<&MemoryValue> {
        self.data.get(key)
    }

    pub fn set(&mut self, key: String, value: MemoryValue) {
        self.data.insert(key, value);
    }

    pub fn remove(&mut self, key: &str) -> Option<MemoryValue> {
        self.data.remove(key)
    }

    pub fn connect(&mut self, memory_id: String) {
        if !self.connections.contains(&memory_id) {
            self.connections.push(memory_id);
        }
    }

    pub fn disconnect(&mut self, memory_id: &str) {
        self.connections.retain(|id| id != memory_id);
    }
}

/// Strategy for recall operations
#[derive(Debug, Clone)]
pub enum RecallStrategy {
    /// Zero-copy access (fastest, but requires type to implement rkyv traits)
    ZeroCopy,
    /// Safe deserialization (slower but works with any serde type)
    Safe,
    /// Raw bytes access
    Raw,
}

/// Graph query builder for powerful recall operations
pub struct GraphBuilder<'a> {
    memories: &'a ActorMemories,
    start: Option<String>,
    depth: usize,
}

impl<'a> GraphBuilder<'a> {
    /// Set the starting node for graph traversal
    pub fn from(mut self, start: &str) -> Self {
        self.start = Some(start.to_string());
        self
    }

    /// Set the maximum depth for traversal
    pub fn depth(mut self, depth: usize) -> Self {
        self.depth = depth;
        self
    }

    /// Execute the graph query and recall results
    pub async fn recall<T>(&self) -> Vec<T>
    where
        T: Archive,
        T::Archived: for<'b> rkyv::bytecheck::CheckBytes<rkyv::api::high::HighValidator<'b, rkyv::rancor::Error>>
            + rkyv::Deserialize<T, rkyv::rancor::Strategy<rkyv::de::pooling::Pool, rkyv::rancor::Error>>
    {
        if let Some(start) = &self.start {
            let results = self.memories.query(start, self.depth).await;
            let mut values = Vec::new();

            for result in results {
                if let Some(value) = self.memories.recall::<T>(&result.memory_id).await {
                    values.push(value);
                }
            }
            values
        } else {
            Vec::new()
        }
    }
}

/// Reactions query builder for powerful recall operations
pub struct ReactionsBuilder<'a> {
    memories: &'a ActorMemories,
    from_idx: usize,
    to_idx: Option<usize>,
}

impl<'a> ReactionsBuilder<'a> {
    /// Set the starting index for reactions query
    pub fn from(mut self, idx: usize) -> Self {
        self.from_idx = idx;
        self
    }

    /// Set the ending index for reactions query
    pub fn to(mut self, idx: usize) -> Self {
        self.to_idx = Some(idx);
        self
    }

    /// Execute the reactions query and recall results with zero-copy access
    pub async fn recall<T>(&self) -> Vec<T>
    where
        T: Archive,
        T::Archived: for<'b> rkyv::bytecheck::CheckBytes<rkyv::api::high::HighValidator<'b, rkyv::rancor::Error>>
            + rkyv::Deserialize<T, rkyv::rancor::Strategy<rkyv::de::pooling::Pool, rkyv::rancor::Error>>
    {
        let to_idx = self.to_idx.unwrap_or_else(|| {
            // Get current reaction log length
            futures::executor::block_on(async {
                self.memories.reaction_log.decided().await
            })
        });

        let reactions = self.memories.reaction_log.get_reactions_from(self.from_idx).await;
        let mut values = Vec::new();

        for (idx, reaction_bytes) in reactions.iter().enumerate() {
            let actual_idx = self.from_idx + idx;
            if actual_idx >= to_idx {
                break;
            }

            if let Ok(value) = rkyv::api::high::from_bytes::<T, rkyv::rancor::Error>(reaction_bytes) {
                values.push(value);
            }
        }

        values
    }


}

/// Integrated memories system for actors with fault tolerance
#[derive(Clone)]
pub struct ActorMemories {
    memories: Arc<RwLock<HashMap<String, Vec<u8>>>>, // memory_id -> serialized Memory
    /// Fault-tolerant reaction log (single source of truth)
    reaction_log: ReactionLog,
    /// Last applied reaction index (for recovery)
    last_applied_idx: Arc<RwLock<usize>>,
}

impl ActorMemories {
    /// Create a new memories system with fault tolerance
    pub async fn new() -> TamtilResult<Self> {
        Ok(Self {
            memories: Arc::new(RwLock::new(HashMap::new())),
            reaction_log: ReactionLog::new(),
            last_applied_idx: Arc::new(RwLock::new(0)),
        })
    }

    /// Recover from reaction log (replay all reactions to rebuild state)
    pub async fn recover(&self) -> TamtilResult<()> {
        let last_applied = *self.last_applied_idx.read().await;
        let reactions = self.reaction_log.get_reactions_from(last_applied).await;

        tracing::info!("Recovering from {} reactions starting at index {}", reactions.len(), last_applied);

        for (idx, _reaction_bytes) in reactions.iter().enumerate() {
            // Update the last applied index for recovery tracking
            let reaction_idx = last_applied + idx + 1;
            let mut last_applied_lock = self.last_applied_idx.write().await;
            *last_applied_lock = reaction_idx;
        }

        tracing::info!("Recovery completed. Last applied index: {}", *self.last_applied_idx.read().await);
        Ok(())
    }

    /// Get the reaction log for advanced operations
    pub fn reaction_log(&self) -> &ReactionLog {
        &self.reaction_log
    }

    /// Get or create a memory by ID
    async fn memory(&self, memory_id: &str) -> TamtilResult<Memory> {
        let memories = self.memories.read().await;

        if let Some(memory_bytes) = memories.get(memory_id) {
            // Deserialize existing memory
            from_bytes::<Memory, RancorError>(memory_bytes)
                .map_err(|e| TamtilError::Deserialization {
                    message: format!("Failed to deserialize memory {}: {}", memory_id, e)
                })
        } else {
            // Create new memory
            Ok(Memory::new(memory_id.to_string()))
        }
    }

    /// Save a memory back to storage
    async fn save_memory(&self, memory: &Memory) -> TamtilResult<()> {
        let memory_bytes = to_bytes::<RancorError>(memory)
            .map_err(|e| TamtilError::Serialization {
                message: format!("Failed to serialize memory {}: {}", memory.id, e)
            })?;

        let mut memories = self.memories.write().await;
        memories.insert(memory.id.clone(), memory_bytes.to_vec());
        Ok(())
    }

    /// Remember memory operations directly (lower-level API)
    pub async fn remember(&self, operations: Vec<MemoryOperation>) -> TamtilResult<()> {
        self.apply(operations).await
    }

    /// Remember a reaction - higher level API that accepts reactions and stores them using reaction.remember()
    /// This is the fault-tolerant event sourcing approach - reactions are the single source of truth
    pub async fn remember_reaction<R>(&self, reaction: &R) -> TamtilResult<()>
    where
        R: Reaction + for<'a> rkyv::Serialize<rkyv::api::high::HighSerializer<rkyv::util::AlignedVec, rkyv::ser::allocator::ArenaHandle<'a>, rkyv::rancor::Error>>
    {
        // First, serialize and store the reaction in the reaction log (event sourcing)
        let reaction_bytes = rkyv::api::high::to_bytes::<RancorError>(reaction)
            .map_err(|e| TamtilError::Serialization {
                message: format!("Failed to serialize reaction for event log: {}", e)
            })?;

        let reaction_idx = self.reaction_log.append(reaction_bytes.to_vec()).await?;

        // Then apply the reaction's memory operations to the projected state
        let operations = reaction.remember();
        let operations_count = operations.len();
        self.apply(operations).await?;

        // Update the last applied index
        let mut last_applied = self.last_applied_idx.write().await;
        *last_applied = reaction_idx;

        tracing::debug!("Remembered reaction at index {} with {} operations", reaction_idx, operations_count);
        Ok(())
    }

    /// Atomic remember operation - either all succeed or all fail (ACID properties)
    /// This is the core consensus operation that ensures consistency
    pub async fn remember_and_serialize<R>(&self, reaction: &R) -> TamtilResult<Vec<u8>>
    where
        R: Reaction + for<'a> rkyv::Serialize<rkyv::api::high::HighSerializer<rkyv::util::AlignedVec, rkyv::ser::allocator::ArenaHandle<'a>, rkyv::rancor::Error>>
    {
        // PHASE 1: Serialize reaction (prepare phase)
        let reaction_bytes = rkyv::api::high::to_bytes::<RancorError>(reaction)
            .map_err(|e| TamtilError::Serialization {
                message: format!("Failed to serialize reaction for event log: {}", e)
            })?;

        let reaction_vec = reaction_bytes.to_vec();
        let operations = reaction.remember();
        let operations_count = operations.len();

        // PHASE 2: Atomic commit - all operations succeed or all fail
        // In distributed mode, this would be the consensus decision point
        let reaction_idx = match self.atomic_commit(reaction_vec.clone(), operations).await {
            Ok(idx) => idx,
            Err(e) => {
                tracing::error!("Atomic commit failed: {}", e);
                return Err(e);
            }
        };

        // PHASE 3: Update last applied index (post-commit)
        let mut last_applied = self.last_applied_idx.write().await;
        *last_applied = reaction_idx;

        tracing::debug!("Atomically committed reaction at index {} with {} operations", reaction_idx, operations_count);
        Ok(reaction_vec)
    }

    /// Atomic commit operation - ensures ACID properties
    async fn atomic_commit(&self, reaction_bytes: Vec<u8>, operations: Vec<MemoryOperation>) -> TamtilResult<usize> {
        // In a distributed system, this would involve consensus (OmniPaxos)
        // For now, we simulate atomic behavior with careful ordering

        // Step 1: Append to reaction log (this is the commit point)
        let reaction_idx = self.reaction_log.append(reaction_bytes).await?;

        // Step 2: Apply memory operations (if this fails, we need to rollback)
        match self.apply(operations).await {
            Ok(()) => Ok(reaction_idx),
            Err(e) => {
                // In a real system, we'd need to implement rollback
                // For now, we log the error and propagate it
                tracing::error!("Failed to apply memory operations after commit: {}", e);
                Err(e)
            }
        }
    }

    /// Remember multiple reactions atomically
    pub async fn remember_reactions<R: Reaction>(&self, reactions: &[R]) -> TamtilResult<()> {
        let mut all_operations = Vec::new();
        for reaction in reactions {
            all_operations.extend(reaction.remember());
        }
        self.apply(all_operations).await
    }

    /// Remember (persist) memory operations atomically - lower level API
    pub async fn apply(&self, operations: Vec<MemoryOperation>) -> TamtilResult<()> {
        if operations.is_empty() {
            return Ok(());
        }

        // Collect all affected memories first to validate operations
        let mut affected_memories = HashMap::new();

        // Pre-validate all operations and collect affected memories
        for operation in &operations {
            match operation {
                MemoryOperation::Create { key, .. } |
                MemoryOperation::Update { key, .. } |
                MemoryOperation::Delete { key } => {
                    let (memory_id, _) = if let Some(pos) = key.find(':') {
                        (&key[..pos], &key[pos + 1..])
                    } else {
                        ("default", key.as_str())
                    };

                    if !affected_memories.contains_key(memory_id) {
                        let memory = self.memory(memory_id).await?;
                        affected_memories.insert(memory_id.to_string(), memory);
                    }
                }
                MemoryOperation::Link { from, to, .. } |
                MemoryOperation::Unlink { from, to, .. } => {
                    if !affected_memories.contains_key(from) {
                        let memory = self.memory(from).await?;
                        affected_memories.insert(from.clone(), memory);
                    }
                    if !affected_memories.contains_key(to) {
                        let memory = self.memory(to).await?;
                        affected_memories.insert(to.clone(), memory);
                    }
                }
            }
        }

        // Apply all operations to the in-memory copies
        for operation in operations {
            match operation {
                MemoryOperation::Create { key, value } | MemoryOperation::Update { key, value } => {
                    let (memory_id, memory_key) = if let Some(pos) = key.find(':') {
                        (&key[..pos], &key[pos + 1..])
                    } else {
                        ("default", key.as_str())
                    };

                    if let Some(memory) = affected_memories.get_mut(memory_id) {
                        memory.set(memory_key.to_string(), value);
                        tracing::debug!("Applied {}:{}", memory_id, memory_key);
                    }
                }
                MemoryOperation::Delete { key } => {
                    let (memory_id, memory_key) = if let Some(pos) = key.find(':') {
                        (&key[..pos], &key[pos + 1..])
                    } else {
                        ("default", key.as_str())
                    };

                    if let Some(memory) = affected_memories.get_mut(memory_id) {
                        memory.remove(memory_key);
                        tracing::debug!("Deleted {}:{}", memory_id, memory_key);
                    }
                }
                MemoryOperation::Link { from, to, relation: _ } => {
                    if let Some(from_memory) = affected_memories.get_mut(&from) {
                        from_memory.connect(to.clone());
                        tracing::debug!("Connected {} -> {}", from, to);
                    }
                }
                MemoryOperation::Unlink { from, to, relation: _ } => {
                    if let Some(from_memory) = affected_memories.get_mut(&from) {
                        from_memory.disconnect(&to);
                        tracing::debug!("Disconnected {} -> {}", from, to);
                    }
                }
            }
        }

        // Atomically save all affected memories
        for (_, memory) in affected_memories {
            self.save_memory(&memory).await?;
        }

        Ok(())
    }

    /// Recall a value by key with zero-copy access using rkyv high-level API
    pub async fn recall<T>(&self, key: &str) -> Option<T>
    where
        T: Archive,
        T::Archived: for<'a> rkyv::bytecheck::CheckBytes<rkyv::api::high::HighValidator<'a, rkyv::rancor::Error>>
            + rkyv::Deserialize<T, rkyv::rancor::Strategy<rkyv::de::pooling::Pool, rkyv::rancor::Error>>
    {
        let (memory_id, memory_key) = if let Some(pos) = key.find(':') {
            (&key[..pos], &key[pos + 1..])
        } else {
            ("default", key)
        };

        if let Ok(memory) = self.memory(memory_id).await {
            if let Some(memory_value) = memory.get(memory_key) {
                // Use rkyv's high-level checked API for safe deserialization
                match rkyv::api::high::from_bytes::<T, RancorError>(&memory_value.bytes) {
                    Ok(value) => Some(value),
                    Err(_) => None,
                }
            } else {
                None
            }
        } else {
            None
        }
    }

    /// Get connected memories (graph traversal)
    pub async fn connected(&self, memory_id: &str) -> Vec<String> {
        if let Ok(memory) = self.memory(memory_id).await {
            memory.connections.clone()
        } else {
            Vec::new()
        }
    }

    /// Create a graph query builder
    pub fn graph(&self) -> GraphBuilder {
        GraphBuilder {
            memories: self,
            start: None,
            depth: 5,
        }
    }

    /// Create a reactions query builder
    pub fn reactions(&self) -> ReactionsBuilder {
        ReactionsBuilder {
            memories: self,
            from_idx: 0,
            to_idx: None,
        }
    }

    /// Query the graph using BFS algorithm (adapted from petgraph)
    pub async fn query(&self, start_memory_id: &str, max_depth: usize) -> Vec<TraversalResult> {
        let mut visited = HashSet::new();
        let mut queue = VecDeque::new();
        let mut results = Vec::new();

        queue.push_back((start_memory_id.to_string(), 0, vec![start_memory_id.to_string()], 0.0));

        while let Some((memory_id, depth, path, total_weight)) = queue.pop_front() {
            if visited.contains(&memory_id) || depth > max_depth {
                continue;
            }

            visited.insert(memory_id.clone());
            results.push(TraversalResult {
                memory_id: memory_id.clone(),
                distance: depth,
                path: path.clone(),
                total_weight,
            });

            if depth < max_depth {
                let connected = self.connected(&memory_id).await;
                for connected_id in connected {
                    if !visited.contains(&connected_id) {
                        let mut new_path = path.clone();
                        new_path.push(connected_id.clone());
                        queue.push_back((connected_id, depth + 1, new_path, total_weight + 1.0));
                    }
                }
            }
        }

        results
    }

    /// Clear all memories (useful for testing)
    pub async fn clear(&self) -> TamtilResult<()> {
        let mut memories = self.memories.write().await;
        memories.clear();
        tracing::debug!("Cleared all memories");
        Ok(())
    }
}

/// The core Actor trait following Alice Ryhl's pattern with integrated memories
/// Actions contain the logic and produce reactions when executed
/// The memories API is passed to actions so developers can control when reactions are remembered
#[async_trait]
pub trait Actor: Send + Sync + 'static + std::any::Any {
    type Action: Action + fmt::Debug;

    /// Process an action by delegating to the action's act method
    /// The action contains the logic and produces a reaction
    /// The memories API handles serialization behind the scenes using rkyv high API
    async fn act(&self, action: Self::Action, actors: &Actors, memories: &ActorMemories) -> TamtilResult<Vec<u8>> {
        // Delegate to the action's act method to get the type-safe reaction
        let reaction = action.act(actors, memories).await?;

        // Use memories to remember and serialize the reaction using rkyv high API
        memories.remember_and_serialize(&reaction).await
    }
}

/// Type-safe message envelope that preserves rkyv's type information
#[derive(Archive, rkyv::Serialize, rkyv::Deserialize)]
pub struct TypedMessage<A> {
    pub action: A,
    pub request_id: Option<u64>, // None for tell, Some for ask
    pub sender_id: ActorId,
}

/// Internal actor messages for lifecycle management
pub enum ActorMessage<A: Action> {
    TypedAction(TypedMessage<A>, oneshot::Sender<TamtilResult<Vec<u8>>>),
    Shutdown,
}

/// Actor Task - the actual actor implementation following Alice Ryhl's pattern
pub struct ActorTask<T: Actor> {
    pub id: ActorId,
    pub actor: T,
    pub receiver: mpsc::Receiver<ActorMessage<T::Action>>,
    pub actors: Actors,
    pub memories: ActorMemories,
}

impl<T: Actor> ActorTask<T> {
    pub fn new(
        id: ActorId,
        actor: T,
        receiver: mpsc::Receiver<ActorMessage<T::Action>>,
        actors: Actors,
        memories: ActorMemories,
    ) -> Self {
        Self {
            id,
            actor,
            receiver,
            actors,
            memories,
        }
    }

    /// Run the actor task - this is where the magic happens
    pub async fn run(mut self) -> TamtilResult<()> {
        tracing::info!("Actor {} starting", self.id.as_str());

        while let Some(message) = self.receiver.recv().await {
            match message {
                ActorMessage::TypedAction(typed_msg, response_tx) => {
                    // Process the action - reaction is already serialized inside act()
                    let result = self.actor.act(typed_msg.action, &self.actors, &self.memories).await;

                    // Simply forward the serialized reaction bytes (zero-copy!)
                    let _ = response_tx.send(result);
                }
                ActorMessage::Shutdown => {
                    tracing::info!("Actor {} shutting down", self.id.as_str());
                    break;
                }

            }
        }

        tracing::info!("Actor {} stopped", self.id.as_str());
        Ok(())
    }
}

/// Actor Handle - the external interface following Alice Ryhl's pattern
#[derive(Clone)]
pub struct ActorHandle<T: Actor> {
    pub id: ActorId,
    pub sender: mpsc::Sender<ActorMessage<T::Action>>,
}

impl<T: Actor> ActorHandle<T> {
    pub fn new(
        id: ActorId,
        sender: mpsc::Sender<ActorMessage<T::Action>>,
    ) -> Self {
        Self { id, sender }
    }

    /// Send an action without waiting for response (tell pattern) - fully type-safe
    pub async fn tell(&self, action: T::Action) -> TamtilResult<()> {
        let typed_msg = TypedMessage {
            action,
            request_id: None,
            sender_id: self.id.clone(),
        };

        let (response_tx, _response_rx) = oneshot::channel();

        self.sender
            .send(ActorMessage::TypedAction(typed_msg, response_tx))
            .await
            .map_err(|_| TamtilError::Communication {
                message: format!("Failed to send message to actor {}", self.id.as_str())
            })?;

        Ok(())
    }

    /// Send an action and wait for reaction (ask pattern) - zero-copy access
    /// Returns the serialized reaction bytes for zero-copy access via rkyv
    pub async fn ask(&self, action: T::Action) -> TamtilResult<Vec<u8>> {
        let request_id = std::time::SystemTime::now()
            .duration_since(std::time::UNIX_EPOCH)
            .unwrap()
            .as_nanos() as u64;

        let typed_msg = TypedMessage {
            action,
            request_id: Some(request_id),
            sender_id: self.id.clone(),
        };

        let (response_tx, response_rx) = oneshot::channel();

        self.sender
            .send(ActorMessage::TypedAction(typed_msg, response_tx))
            .await
            .map_err(|_| TamtilError::Communication {
                message: format!("Failed to send message to actor {}", self.id.as_str())
            })?;

        match response_rx.await {
            Ok(reaction_bytes) => reaction_bytes,
            Err(_) => Err(TamtilError::Communication {
                message: format!("Actor {} did not respond", self.id.as_str())
            }),
        }
    }

    /// Access the reaction data via zero-copy rkyv (developers use this for type-safe access)
    pub fn access_reaction<R: Archive>(reaction_bytes: &[u8]) -> TamtilResult<&R::Archived> {
        use rkyv::api::access_unchecked;
        // SAFETY: In production, we'd validate this data first
        // For now, we trust that the actor serialized it correctly
        unsafe {
            Ok(access_unchecked::<R::Archived>(reaction_bytes))
        }
    }

    /// Shutdown the actor gracefully
    pub async fn shutdown(&self) -> TamtilResult<()> {
        self.sender
            .send(ActorMessage::Shutdown)
            .await
            .map_err(|_| TamtilError::Communication {
                message: format!("Failed to send shutdown to actor {}", self.id.as_str())
            })?;
        Ok(())
    }
}

// Implement ErasedActorHandle for ActorHandle
#[async_trait]
impl<T: Actor> ErasedActorHandle for ActorHandle<T> {
    fn actor_id(&self) -> &ActorId {
        &self.id
    }

    async fn shutdown(&self) -> TamtilResult<()> {
        ActorHandle::shutdown(self).await
    }
}

/// Builder pattern for creating actors with integrated memories and actions
/// This shifts the usage from letting developers create actors to offering pre-implemented actors
pub struct ActorBuilder {
    id: ActorId,
    memories: ActorMemories,
}

impl ActorBuilder {
    /// Create a new actor builder with integrated memories
    pub async fn new(id: impl Into<ActorId>) -> TamtilResult<Self> {
        Ok(Self {
            id: id.into(),
            memories: ActorMemories::new().await?,
        })
    }

    /// Get a reference to the actor's memories for configuration
    pub fn memories(&self) -> &ActorMemories {
        &self.memories
    }

    /// Get a mutable reference to the actor's memories for configuration
    pub fn memories_mut(&mut self) -> &mut ActorMemories {
        &mut self.memories
    }

    /// Pre-populate the actor's memories with initial data
    pub async fn with_initial_memory(self, key: &str, value: MemoryValue) -> TamtilResult<Self> {
        let operations = vec![MemoryOperation::Create {
            key: key.to_string(),
            value,
        }];
        self.memories.apply(operations).await?;
        Ok(self)
    }

    /// Connect this actor's memories to another memory graph
    pub async fn connect_to(self, from_memory: &str, to_memory: &str, relation: &str) -> TamtilResult<Self> {
        let operations = vec![MemoryOperation::Link {
            from: from_memory.to_string(),
            to: to_memory.to_string(),
            relation: relation.to_string(),
        }];
        self.memories.apply(operations).await?;
        Ok(self)
    }

    /// Build a concrete actor implementation that can handle the specified action type
    /// This is where developers extend the actor with their business logic
    pub fn build<A: Action + fmt::Debug>(self) -> BuiltActor<A> {
        BuiltActor {
            id: self.id,
            memories: self.memories,
            _phantom: std::marker::PhantomData,
        }
    }
}

/// A concrete actor implementation built with the builder pattern
/// This actor has integrated memories and can handle actions of type A
pub struct BuiltActor<A: Action + fmt::Debug> {
    id: ActorId,
    memories: ActorMemories,
    _phantom: std::marker::PhantomData<A>,
}

#[async_trait]
impl<A: Action + fmt::Debug> Actor for BuiltActor<A> {
    type Action = A;

    async fn act(&self, action: Self::Action, actors: &Actors, _memories: &ActorMemories) -> TamtilResult<Vec<u8>> {
        // Use the integrated memories instead of the passed-in memories
        // Get the type-safe reaction from the action
        let reaction = action.act(actors, &self.memories).await?;

        // Use memories to remember and serialize the reaction using rkyv high API
        self.memories.remember_and_serialize(&reaction).await
    }
}

impl<A: Action + fmt::Debug> BuiltActor<A> {
    /// Get the actor's ID
    pub fn id(&self) -> &ActorId {
        &self.id
    }

    /// Get access to the actor's integrated memories
    pub fn memories(&self) -> &ActorMemories {
        &self.memories
    }

    /// Recall a value from the actor's memories with zero-copy access
    pub async fn recall<T>(&self, key: &str) -> Option<T>
    where
        T: Archive,
        T::Archived: for<'a> rkyv::bytecheck::CheckBytes<rkyv::api::high::HighValidator<'a, rkyv::rancor::Error>>
            + rkyv::Deserialize<T, rkyv::rancor::Strategy<rkyv::de::pooling::Pool, rkyv::rancor::Error>>
    {
        self.memories.recall(key).await
    }

    /// Remember a reaction in the actor's memories
    pub async fn remember_reaction<R>(&self, reaction: &R) -> TamtilResult<()>
    where
        R: Reaction + for<'a> rkyv::Serialize<rkyv::api::high::HighSerializer<rkyv::util::AlignedVec, rkyv::ser::allocator::ArenaHandle<'a>, rkyv::rancor::Error>>
    {
        self.memories.remember_reaction(reaction).await
    }

    /// Remember multiple reactions atomically in the actor's memories
    pub async fn remember_reactions<R: Reaction>(&self, reactions: &[R]) -> TamtilResult<()> {
        self.memories.remember_reactions(reactions).await
    }

    /// Remember operations in the actor's memories (lower level API)
    pub async fn apply(&self, operations: Vec<MemoryOperation>) -> TamtilResult<()> {
        self.memories.apply(operations).await
    }

    /// Query the actor's memory graph using BFS
    pub async fn query(&self, start_memory_id: &str, max_depth: usize) -> Vec<TraversalResult> {
        self.memories.query(start_memory_id, max_depth).await
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    /// Comprehensive blackbox tests for TAMTIL actor system
    /// These tests only interact with the public API and test the system as a blackbox

    // Test data structures for blackbox testing
    #[derive(Debug, Clone, Archive, Serialize, Deserialize)]
    #[rkyv(derive(Debug))]
    struct TestAction {
        command: String,
        value: i32,
    }

    #[derive(Debug, Clone, Archive, Serialize, Deserialize)]
    #[rkyv(derive(Debug))]
    struct TestReaction {
        result: String,
        computed_value: i32,
    }

    impl Reaction for TestReaction {
        fn remember(&self) -> Vec<MemoryOperation> {
            vec![
                MemoryOperation::Create {
                    key: format!("results:{}", self.computed_value),
                    value: MemoryValue::new(&self.result).unwrap(),
                },
                // Use Update instead of Create to ensure the value is always updated
                MemoryOperation::Update {
                    key: "last_computation".to_string(),
                    value: MemoryValue::new(&self.computed_value).unwrap(),
                },
            ]
        }
    }

    impl Action for TestAction {
        type Reaction = TestReaction;

        fn act(
            &self,
            _actors: &Actors,
            memories: &ActorMemories,
        ) -> impl std::future::Future<Output = TamtilResult<Self::Reaction>> + Send {
            let command = self.command.clone();
            let value = self.value;

            async move {
                let result = match command.as_str() {
                    "add" => {
                        let last: Option<i32> = memories.recall("last_computation").await;
                        let computed = last.unwrap_or(0) + value;
                        TestReaction {
                            result: format!("Added {} to get {}", value, computed),
                            computed_value: computed,
                        }
                    }
                    "multiply" => {
                        let last: Option<i32> = memories.recall("last_computation").await;
                        let computed = last.unwrap_or(1) * value;
                        TestReaction {
                            result: format!("Multiplied by {} to get {}", value, computed),
                            computed_value: computed,
                        }
                    }
                    "reset" => {
                        TestReaction {
                            result: "Reset to zero".to_string(),
                            computed_value: 0,
                        }
                    }
                    _ => {
                        TestReaction {
                            result: "Unknown command".to_string(),
                            computed_value: -1,
                        }
                    }
                };

                // Return the type-safe reaction - serialization handled by Actor trait
                Ok(result)
            }
        }
    }

    /// Test 1: Basic actor creation and action processing
    #[tokio::test]
    async fn test_basic_actor_functionality() {
        let actors = Actors::new().await.unwrap();
        let actor_id = ActorId::new("calculator");

        let built_actor = ActorBuilder::new(actor_id.clone()).await.unwrap().build::<TestAction>();
        actors.spawn(actor_id.clone(), built_actor).await.unwrap();

        // Test basic action processing
        let action = TestAction {
            command: "add".to_string(),
            value: 5,
        };

        let reaction_bytes = actors.actor(&actor_id).act(action).await.unwrap();
        assert!(!reaction_bytes.is_empty(), "Reaction should not be empty");

        // Verify we can deserialize the reaction
        let reaction: TestReaction = rkyv::api::high::from_bytes::<TestReaction, rkyv::rancor::Error>(&reaction_bytes).unwrap();
        assert_eq!(reaction.computed_value, 5);
        assert!(reaction.result.contains("Added 5"));
    }

    /// Test 2: State persistence across multiple actions
    #[tokio::test]
    async fn test_state_persistence() {
        let actors = Actors::new().await.unwrap();
        let actor_id = ActorId::new("persistent_calculator");

        let built_actor = ActorBuilder::new(actor_id.clone()).await.unwrap().build::<TestAction>();
        actors.spawn(actor_id.clone(), built_actor).await.unwrap();

        // First action: add 10
        let action1 = TestAction {
            command: "add".to_string(),
            value: 10,
        };
        let reaction_bytes1 = actors.actor(&actor_id).act(action1).await.unwrap();
        let reaction1: TestReaction = rkyv::api::high::from_bytes::<TestReaction, rkyv::rancor::Error>(&reaction_bytes1).unwrap();
        assert_eq!(reaction1.computed_value, 10);

        // Second action: multiply by 3 (should use previous result)
        let action2 = TestAction {
            command: "multiply".to_string(),
            value: 3,
        };
        let reaction_bytes2 = actors.actor(&actor_id).act(action2).await.unwrap();
        let reaction2: TestReaction = rkyv::api::high::from_bytes::<TestReaction, rkyv::rancor::Error>(&reaction_bytes2).unwrap();
        assert_eq!(reaction2.computed_value, 30); // 10 * 3

        // Third action: add 5 (should use previous result)
        let action3 = TestAction {
            command: "add".to_string(),
            value: 5,
        };
        let reaction_bytes3 = actors.actor(&actor_id).act(action3).await.unwrap();
        let reaction3: TestReaction = rkyv::api::high::from_bytes::<TestReaction, rkyv::rancor::Error>(&reaction_bytes3).unwrap();
        assert_eq!(reaction3.computed_value, 35); // 30 + 5
    }

    /// Test 3: Error handling and edge cases
    #[tokio::test]
    async fn test_error_handling() {
        let actors = Actors::new().await.unwrap();
        let actor_id = ActorId::new("error_test_actor");

        let built_actor = ActorBuilder::new(actor_id.clone()).await.unwrap().build::<TestAction>();
        actors.spawn(actor_id.clone(), built_actor).await.unwrap();

        // Test unknown command
        let action = TestAction {
            command: "unknown_command".to_string(),
            value: 42,
        };

        let reaction_bytes = actors.actor(&actor_id).act(action).await.unwrap();
        let reaction: TestReaction = rkyv::api::high::from_bytes::<TestReaction, rkyv::rancor::Error>(&reaction_bytes).unwrap();
        assert_eq!(reaction.computed_value, -1);
        assert_eq!(reaction.result, "Unknown command");
    }

    /// Test 4: Zero value handling
    #[tokio::test]
    async fn test_zero_value_handling() {
        let actors = Actors::new().await.unwrap();
        let actor_id = ActorId::new("zero_test_actor");

        let built_actor = ActorBuilder::new(actor_id.clone()).await.unwrap().build::<TestAction>();
        actors.spawn(actor_id.clone(), built_actor).await.unwrap();

        // Test adding zero
        let action = TestAction {
            command: "add".to_string(),
            value: 0,
        };

        let reaction_bytes = actors.actor(&actor_id).act(action).await.unwrap();
        let reaction: TestReaction = rkyv::api::high::from_bytes::<TestReaction, rkyv::rancor::Error>(&reaction_bytes).unwrap();
        assert_eq!(reaction.computed_value, 0);

        // Test multiplying by zero
        let action2 = TestAction {
            command: "multiply".to_string(),
            value: 0,
        };

        let reaction_bytes2 = actors.actor(&actor_id).act(action2).await.unwrap();
        let reaction2: TestReaction = rkyv::api::high::from_bytes::<TestReaction, rkyv::rancor::Error>(&reaction_bytes2).unwrap();
        assert_eq!(reaction2.computed_value, 0);
    }

    /// Test 5: Negative value handling
    #[tokio::test]
    async fn test_negative_value_handling() {
        let actors = Actors::new().await.unwrap();
        let actor_id = ActorId::new("negative_test_actor");

        let built_actor = ActorBuilder::new(actor_id.clone()).await.unwrap().build::<TestAction>();
        actors.spawn(actor_id.clone(), built_actor).await.unwrap();

        // Start with positive value
        let action1 = TestAction {
            command: "add".to_string(),
            value: 10,
        };
        actors.actor(&actor_id).act(action1).await.unwrap();

        // Add negative value
        let action2 = TestAction {
            command: "add".to_string(),
            value: -15,
        };

        let reaction_bytes = actors.actor(&actor_id).act(action2).await.unwrap();
        let reaction: TestReaction = rkyv::api::high::from_bytes::<TestReaction, rkyv::rancor::Error>(&reaction_bytes).unwrap();
        assert_eq!(reaction.computed_value, -5); // 10 + (-15) = -5
    }

    /// Test 6: Reset functionality
    #[tokio::test]
    async fn test_reset_functionality() {
        let actors = Actors::new().await.unwrap();
        let actor_id = ActorId::new("reset_test_actor");

        let built_actor = ActorBuilder::new(actor_id.clone()).await.unwrap().build::<TestAction>();
        actors.spawn(actor_id.clone(), built_actor).await.unwrap();

        // Build up some state
        let action1 = TestAction {
            command: "add".to_string(),
            value: 100,
        };
        actors.actor(&actor_id).act(action1).await.unwrap();

        let action2 = TestAction {
            command: "multiply".to_string(),
            value: 5,
        };
        actors.actor(&actor_id).act(action2).await.unwrap();

        // Reset
        let reset_action = TestAction {
            command: "reset".to_string(),
            value: 999, // Value should be ignored for reset
        };

        let reaction_bytes = actors.actor(&actor_id).act(reset_action).await.unwrap();
        let reaction: TestReaction = rkyv::api::high::from_bytes::<TestReaction, rkyv::rancor::Error>(&reaction_bytes).unwrap();
        assert_eq!(reaction.computed_value, 0);
        assert_eq!(reaction.result, "Reset to zero");

        // Verify next operation starts from zero
        let action3 = TestAction {
            command: "add".to_string(),
            value: 7,
        };

        let reaction_bytes3 = actors.actor(&actor_id).act(action3).await.unwrap();
        let reaction3: TestReaction = rkyv::api::high::from_bytes::<TestReaction, rkyv::rancor::Error>(&reaction_bytes3).unwrap();
        assert_eq!(reaction3.computed_value, 7); // Should start from 0, not previous state
    }

    /// Test 7: Multiple actors with different IDs
    #[tokio::test]
    async fn test_multiple_actors() {
        let actors = Actors::new().await.unwrap();

        // Create two different actors
        let actor1_id = ActorId::new("calculator_1");
        let actor2_id = ActorId::new("calculator_2");

        let built_actor1 = ActorBuilder::new(actor1_id.clone()).await.unwrap().build::<TestAction>();
        let built_actor2 = ActorBuilder::new(actor2_id.clone()).await.unwrap().build::<TestAction>();

        actors.spawn(actor1_id.clone(), built_actor1).await.unwrap();
        actors.spawn(actor2_id.clone(), built_actor2).await.unwrap();

        // Each actor should maintain separate state
        let action1 = TestAction {
            command: "add".to_string(),
            value: 10,
        };

        let action2 = TestAction {
            command: "add".to_string(),
            value: 20,
        };

        // Send different actions to different actors
        let reaction_bytes1 = actors.actor(&actor1_id).act(action1).await.unwrap();
        let reaction_bytes2 = actors.actor(&actor2_id).act(action2).await.unwrap();

        let reaction1: TestReaction = rkyv::api::high::from_bytes::<TestReaction, rkyv::rancor::Error>(&reaction_bytes1).unwrap();
        let reaction2: TestReaction = rkyv::api::high::from_bytes::<TestReaction, rkyv::rancor::Error>(&reaction_bytes2).unwrap();

        // Verify they have different states
        assert_eq!(reaction1.computed_value, 10);
        assert_eq!(reaction2.computed_value, 20);

        // Verify state isolation - actor1 should still have 10
        let action3 = TestAction {
            command: "add".to_string(),
            value: 5,
        };

        let reaction_bytes3 = actors.actor(&actor1_id).act(action3).await.unwrap();
        let reaction3: TestReaction = rkyv::api::high::from_bytes::<TestReaction, rkyv::rancor::Error>(&reaction_bytes3).unwrap();
        assert_eq!(reaction3.computed_value, 15); // 10 + 5, not affected by actor2
    }

    /// Test 8: Comprehensive integration test with complex workflows
    #[tokio::test]
    async fn test_comprehensive_integration() {
        let actors = Actors::new().await.unwrap();
        let actor_id = ActorId::new("integration_test_actor");

        let built_actor = ActorBuilder::new(actor_id.clone()).await.unwrap().build::<TestAction>();
        actors.spawn(actor_id.clone(), built_actor).await.unwrap();

        // Test a complex workflow: multiple operations with dependencies
        let operations = vec![
            ("add", 100),
            ("multiply", 2),
            ("add", -50),
            ("multiply", 3),
            ("reset", 0),
            ("add", 42),
        ];

        let expected_values = vec![100, 200, 150, 450, 0, 42];

        for (i, (command, value)) in operations.iter().enumerate() {
            let action = TestAction {
                command: command.to_string(),
                value: *value,
            };

            let reaction_bytes = actors.actor(&actor_id).act(action).await.unwrap();
            let reaction: TestReaction = rkyv::api::high::from_bytes::<TestReaction, rkyv::rancor::Error>(&reaction_bytes).unwrap();

            assert_eq!(reaction.computed_value, expected_values[i],
                      "Operation {} failed: expected {}, got {}",
                      i, expected_values[i], reaction.computed_value);
        }
    }

    /// Test 9: Hierarchical actor IDs and relationships
    #[tokio::test]
    async fn test_hierarchical_actor_ids() {
        // Test parent/child relationships
        let root_id = ActorId::new("root");
        let child_id = ActorId::new("root/child");
        let grandchild_id = ActorId::new("root/child/grandchild");
        let sibling_id = ActorId::new("root/sibling");

        // Test parent() method
        assert_eq!(child_id.parent(), Some(root_id.clone()));
        assert_eq!(grandchild_id.parent(), Some(child_id.clone()));
        assert_eq!(root_id.parent(), None);

        // Test depth() method
        assert_eq!(root_id.depth(), 0);
        assert_eq!(child_id.depth(), 1);
        assert_eq!(grandchild_id.depth(), 2);

        // Test is_child_of() method
        assert!(child_id.is_child_of(&root_id));
        assert!(grandchild_id.is_child_of(&root_id));
        assert!(grandchild_id.is_child_of(&child_id));
        assert!(!sibling_id.is_child_of(&child_id));
        assert!(!root_id.is_child_of(&child_id));

        // Test edge cases
        let empty_id = ActorId::new("");
        assert_eq!(empty_id.depth(), 0);
        assert_eq!(empty_id.parent(), None);
    }

    /// Test 10: Concurrent access and thread safety
    #[tokio::test]
    async fn test_concurrent_access() {
        let actors = Actors::new().await.unwrap();
        let actor_id = ActorId::new("concurrent_test_actor");

        let built_actor = ActorBuilder::new(actor_id.clone()).await.unwrap().build::<TestAction>();
        actors.spawn(actor_id.clone(), built_actor).await.unwrap();

        // Spawn multiple concurrent tasks
        let mut handles = Vec::new();

        for i in 0..10 {
            let actors_clone = actors.clone();
            let actor_id_clone = actor_id.clone();

            let handle = tokio::spawn(async move {
                let action = TestAction {
                    command: "add".to_string(),
                    value: i,
                };

                actors_clone.actor(&actor_id_clone).act(action).await
            });

            handles.push(handle);
        }

        // Wait for all tasks to complete
        let mut results = Vec::new();
        for handle in handles {
            let result = handle.await.unwrap();
            assert!(result.is_ok(), "Concurrent operation failed");
            results.push(result.unwrap());
        }

        // Verify all operations completed
        assert_eq!(results.len(), 10);
        for result in results {
            assert!(!result.is_empty());
        }
    }

    /// Test 11: Memory value serialization edge cases
    #[tokio::test]
    async fn test_memory_value_edge_cases() {
        // Test various data types
        let string_val = MemoryValue::string("test string").unwrap();
        let number_val = MemoryValue::number(42.5).unwrap();
        let boolean_val = MemoryValue::boolean(true).unwrap();

        // Test empty string
        let empty_string = MemoryValue::string("").unwrap();
        assert!(!empty_string.bytes.is_empty());

        // Test zero values
        let zero_number = MemoryValue::number(0.0).unwrap();
        let false_boolean = MemoryValue::boolean(false).unwrap();

        // Test negative numbers
        let negative_number = MemoryValue::number(-123.456).unwrap();

        // Test very large numbers
        let large_number = MemoryValue::number(f64::MAX).unwrap();
        let small_number = MemoryValue::number(f64::MIN).unwrap();

        // All should serialize successfully
        assert!(!string_val.bytes.is_empty());
        assert!(!number_val.bytes.is_empty());
        assert!(!boolean_val.bytes.is_empty());
        assert!(!zero_number.bytes.is_empty());
        assert!(!false_boolean.bytes.is_empty());
        assert!(!negative_number.bytes.is_empty());
        assert!(!large_number.bytes.is_empty());
        assert!(!small_number.bytes.is_empty());
    }

    /// Test 12: Actor not found error handling
    #[tokio::test]
    async fn test_actor_not_found() {
        let actors = Actors::new().await.unwrap();
        let nonexistent_id = ActorId::new("nonexistent_actor");

        let action = TestAction {
            command: "add".to_string(),
            value: 42,
        };

        let result = actors.actor(&nonexistent_id).act(action).await;
        assert!(result.is_err());

        match result.unwrap_err() {
            TamtilError::ActorNotFound { id } => {
                assert_eq!(id, "nonexistent_actor");
            }
            _ => panic!("Expected ActorNotFound error"),
        }
    }

}
