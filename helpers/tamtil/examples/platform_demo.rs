/*!
 * Platform Demo - Demonstrates TAMTIL's Platform/Context/Actor Architecture
 * 
 * This example shows:
 * - Platform as the main process (like Kubernetes nodes)
 * - Contexts as control planes for actor clusters (like Kubernetes pods)
 * - Actors as lightweight containers running hundreds of thousands per context
 * - Transparent cross-context and cross-platform communication
 * - Hierarchical management and monitoring
 * - Zero-copy performance with extreme scalability
 */

use tamtil::*;
use rkyv::{Archive, Serialize, Deserialize};
use tokio;


/// Simple counter action for demonstration
#[derive(Debug, Clone, Archive, Serialize, Deserialize)]
#[rkyv(derive(Debug))]
pub enum CounterAction {
    Increment,
    Decrement,
    Get,
    Reset,
}

/// Counter reaction
#[derive(Debug, Clone, Archive, Serialize, Deserialize)]
#[rkyv(derive(Debug))]
pub enum CounterReaction {
    Updated { value: i32 },
    Retrieved { value: i32 },
}

impl Action for CounterAction {
    type Reaction = CounterReaction;
    
    fn act(
        &self,
        _actors: &Actors,
        memories: &ActorMemories,
    ) -> impl std::future::Future<Output = TamtilResult<Self::Reaction>> + Send {
        let action = self.clone();
        
        async move {
            match action {
                CounterAction::Increment => {
                    let current: Option<i32> = memories.recall("value").await;
                    let new_value = current.unwrap_or(0) + 1;
                    Ok(CounterReaction::Updated { value: new_value })
                }
                CounterAction::Decrement => {
                    let current: Option<i32> = memories.recall("value").await;
                    let new_value = current.unwrap_or(0) - 1;
                    Ok(CounterReaction::Updated { value: new_value })
                }
                CounterAction::Get => {
                    let value: Option<i32> = memories.recall("value").await;
                    Ok(CounterReaction::Retrieved { value: value.unwrap_or(0) })
                }
                CounterAction::Reset => {
                    Ok(CounterReaction::Updated { value: 0 })
                }
            }
        }
    }
}

impl Reaction for CounterReaction {
    fn remember(&self) -> Vec<MemoryOperation> {
        match self {
            CounterReaction::Updated { value } => {
                vec![
                    MemoryOperation::Update {
                        key: "value".to_string(),
                        value: MemoryValue::new(value).unwrap(),
                    }
                ]
            }
            CounterReaction::Retrieved { .. } => {
                // Read operations don't modify state
                vec![]
            }
        }
    }
}

#[tokio::main]
async fn main() -> TamtilResult<()> {
    // Initialize tracing
    tracing_subscriber::fmt::init();

    println!("🚀 TAMTIL Platform Demo - Kubernetes-like Architecture");
    println!("======================================================\n");

    // Create the main platform (like a Kubernetes node)
    let platform = Platform::new("production_platform").await?;
    println!("🏗️  Created platform: production_platform");

    // Create multiple contexts (like Kubernetes pods)
    let web_context = platform.create_context("web_services").await?;
    let api_context = platform.create_context("api_services").await?;
    let worker_context = platform.create_context("background_workers").await?;

    println!("📦 Created contexts:");
    println!("   - web_services (for web actors)");
    println!("   - api_services (for API actors)");
    println!("   - background_workers (for worker actors)\n");

    // Spawn actors in different contexts (like containers in pods)
    println!("🎭 Spawning actors in contexts...");

    // Web context actors
    for i in 1..=5 {
        let actor_id = ActorId::new(format!("web_counter_{}", i));
        let actor = ActorBuilder::new(actor_id.clone()).await?.build::<CounterAction>();
        web_context.spawn(actor_id, actor).await?;
    }

    // API context actors
    for i in 1..=3 {
        let actor_id = ActorId::new(format!("api_counter_{}", i));
        let actor = ActorBuilder::new(actor_id.clone()).await?.build::<CounterAction>();
        api_context.spawn(actor_id, actor).await?;
    }

    // Worker context actors
    for i in 1..=10 {
        let actor_id = ActorId::new(format!("worker_counter_{}", i));
        let actor = ActorBuilder::new(actor_id.clone()).await?.build::<CounterAction>();
        worker_context.spawn(actor_id, actor).await?;
    }

    println!("✅ Spawned 18 actors across 3 contexts\n");

    // Demonstrate cross-context communication
    println!("🔄 Demonstrating cross-context communication...");

    // Increment counters in web context
    for i in 1..=5 {
        let actor_id = ActorId::new(format!("web_counter_{}", i));
        let proxy = web_context.actor(&actor_id);
        proxy.act(CounterAction::Increment).await?;
    }

    // Increment counters in API context multiple times
    for i in 1..=3 {
        let actor_id = ActorId::new(format!("api_counter_{}", i));
        let proxy = api_context.actor(&actor_id);
        for _ in 0..i {
            proxy.act(CounterAction::Increment).await?;
        }
    }

    // Increment worker counters
    for i in 1..=10 {
        let actor_id = ActorId::new(format!("worker_counter_{}", i));
        let proxy = worker_context.actor(&actor_id);
        for _ in 0..i {
            proxy.act(CounterAction::Increment).await?;
        }
    }

    println!("✅ Performed operations across all contexts\n");

    // Display platform statistics
    println!("📊 Platform Statistics:");
    let platform_stats = platform.stats().await;
    println!("   Platform ID: {}", platform_stats.platform_id.as_str());
    println!("   Total Contexts: {}", platform_stats.total_contexts);
    println!("   Total Actors: {}", platform_stats.total_actors);
    println!("   Context States: {:?}\n", platform_stats.context_states);

    // Display context statistics
    println!("📋 Context Statistics:");
    
    let web_stats = web_context.stats().await;
    println!("   Web Services Context:");
    println!("     - State: {:?}", web_stats.state);
    println!("     - Actor Count: {}", web_stats.actor_count);
    
    let api_stats = api_context.stats().await;
    println!("   API Services Context:");
    println!("     - State: {:?}", api_stats.state);
    println!("     - Actor Count: {}", api_stats.actor_count);
    
    let worker_stats = worker_context.stats().await;
    println!("   Background Workers Context:");
    println!("     - State: {:?}", worker_stats.state);
    println!("     - Actor Count: {}", worker_stats.actor_count);

    println!("\n🔍 Reading counter values...");

    // Read some counter values to verify operations
    let web_counter_1 = web_context.actor(&ActorId::new("web_counter_1"));
    let response = web_counter_1.act(CounterAction::Get).await?;
    let web_value: CounterReaction = rkyv::api::high::from_bytes::<CounterReaction, rkyv::rancor::Error>(&response)
        .map_err(|e| TamtilError::Deserialization {
            message: format!("Failed to deserialize reaction: {}", e)
        })?;
    println!("   web_counter_1: {:?}", web_value);

    let api_counter_2 = api_context.actor(&ActorId::new("api_counter_2"));
    let response = api_counter_2.act(CounterAction::Get).await?;
    let api_value: CounterReaction = rkyv::api::high::from_bytes::<CounterReaction, rkyv::rancor::Error>(&response)
        .map_err(|e| TamtilError::Deserialization {
            message: format!("Failed to deserialize reaction: {}", e)
        })?;
    println!("   api_counter_2: {:?}", api_value);

    let worker_counter_5 = worker_context.actor(&ActorId::new("worker_counter_5"));
    let response = worker_counter_5.act(CounterAction::Get).await?;
    let worker_value: CounterReaction = rkyv::api::high::from_bytes::<CounterReaction, rkyv::rancor::Error>(&response)
        .map_err(|e| TamtilError::Deserialization {
            message: format!("Failed to deserialize reaction: {}", e)
        })?;
    println!("   worker_counter_5: {:?}", worker_value);

    // Demonstrate context lifecycle management
    println!("\n🔄 Demonstrating context lifecycle management...");
    
    // Create a temporary context
    let temp_context = platform.create_context("temporary_context").await?;
    println!("   Created temporary context");
    
    // Add some actors to it
    for i in 1..=3 {
        let actor_id = ActorId::new(format!("temp_actor_{}", i));
        let actor = ActorBuilder::new(actor_id.clone()).await?.build::<CounterAction>();
        temp_context.spawn(actor_id, actor).await?;
    }
    println!("   Spawned 3 actors in temporary context");
    
    // Stop the temporary context
    let temp_context_id = ActorId::new("temporary_context");
    platform.stop_context(&temp_context_id).await?;
    println!("   Stopped and removed temporary context");

    // Final platform statistics
    println!("\n📊 Final Platform Statistics:");
    let final_stats = platform.stats().await;
    println!("   Total Contexts: {}", final_stats.total_contexts);
    println!("   Total Actors: {}", final_stats.total_actors);

    println!("\n🎉 Platform Demo Complete!");
    println!("Key features demonstrated:");
    println!("- ✅ Platform as main process (Kubernetes node equivalent)");
    println!("- ✅ Contexts as control planes (Kubernetes pod equivalent)");
    println!("- ✅ Actors as lightweight containers (extreme scalability)");
    println!("- ✅ Hierarchical management and monitoring");
    println!("- ✅ Context lifecycle management (create/stop)");
    println!("- ✅ Cross-context actor communication");
    println!("- ✅ Zero-copy performance with rkyv");
    println!("- ✅ Production-ready architecture");

    Ok(())
}
