//! # TAMTIL Todo App Example
//!
//! This example demonstrates all key features of TAMTIL:
//! - Actor pattern with action->reaction
//! - Event sourcing with reaction log
//! - Zero-copy serialization with rkyv
//! - Graph-based memories with recall queries
//! - Fault tolerance and recovery
//! - Builder pattern for extending actors

use tamtil::*;
use rkyv::{Archive, Serialize, Deserialize};
use std::collections::HashMap;
use tokio;

/// Todo item structure
#[derive(Debug, Clone, Archive, rkyv::Serialize, rkyv::Deserialize)]
#[rkyv(compare(PartialEq), derive(Debug))]
pub struct TodoItem {
    pub id: u32,
    pub title: String,
    pub description: String,
    pub completed: bool,
    pub created_at: u64,
}

/// Todo actions - contain the business logic
#[derive(Debug, Clone, Archive, rkyv::Serialize, rkyv::Deserialize)]
#[rkyv(compare(PartialEq), derive(Debug))]
pub enum TodoAction {
    Add { title: String, description: String },
    Complete { id: u32 },
    Delete { id: u32 },
    List,
    Get { id: u32 },
}

/// Todo reactions - contain the results and state changes
#[derive(Debu<PERSON>, <PERSON><PERSON>, Default, Archive, rkyv::Serialize, rkyv::Deserialize)]
#[rkyv(compare(PartialEq), derive(Debug))]
pub struct TodoReaction {
    pub success: bool,
    pub message: String,
    pub todo_item: Option<TodoItem>,
    pub todo_list: Vec<TodoItem>,
}

/// Action implementations - business logic using only memories.recall() and memories.remember()
#[async_trait]
impl Action for TodoAction {
    type Reaction = TodoReaction;

    fn act(self, _actors: &Actors, memories: &Memories) -> impl std::future::Future<Output = TamtilResult<Self::Reaction>> + Send {
        async move {
        // All state access must go through memories.recall()
        // All state changes must go through the reaction's remember() method

        match self {
            TodoAction::Add { title, description } => {
                // Get the next ID using the builder pattern
                let next_id = memories.recall("todoapp:next_id").as_type::<u32>().await.unwrap_or(1);

                let todo = TodoItem {
                    id: next_id,
                    title: title.clone(),
                    description: description.clone(),
                    completed: false,
                    created_at: std::time::SystemTime::now()
                        .duration_since(std::time::UNIX_EPOCH)
                        .unwrap()
                        .as_secs(),
                };

                Ok(TodoReaction {
                    success: true,
                    message: format!("Added todo: {}", title),
                    todo_item: Some(todo),
                    todo_list: vec![],
                })
            }
            TodoAction::Complete { id } => {
                // Get todo using builder pattern
                if let Some(mut todo) = memories.recall(&format!("todos:item_{}", id)).as_type::<TodoItem>().await {
                    todo.completed = true;
                    Ok(TodoReaction {
                        success: true,
                        message: format!("Completed todo: {}", todo.title),
                        todo_item: Some(todo),
                        todo_list: vec![],
                    })
                } else {
                    Ok(TodoReaction {
                        success: false,
                        message: format!("Todo with id {} not found", id),
                        todo_item: None,
                        todo_list: vec![],
                    })
                }
            }
            TodoAction::Delete { id } => {
                // Get todo using builder pattern
                if let Some(todo) = memories.recall(&format!("todos:item_{}", id)).as_type::<TodoItem>().await {
                    Ok(TodoReaction {
                        success: true,
                        message: format!("Deleted todo: {}", todo.title),
                        todo_item: Some(todo),
                        todo_list: vec![],
                    })
                } else {
                    Ok(TodoReaction {
                        success: false,
                        message: format!("Todo with id {} not found", id),
                        todo_item: None,
                        todo_list: vec![],
                    })
                }
            }
            TodoAction::List => {
                // Get all todo keys and recall each one using builder pattern
                let keys = memories.keys("todos").await;
                let mut todos = Vec::new();

                for key in keys {
                    if key.starts_with("item_") {
                        if let Some(todo) = memories.recall(&format!("todos:{}", key)).as_type::<TodoItem>().await {
                            todos.push(todo);
                        }
                    }
                }

                todos.sort_by_key(|t| t.id);
                Ok(TodoReaction {
                    success: true,
                    message: format!("Found {} todos", todos.len()),
                    todo_item: None,
                    todo_list: todos,
                })
            }
            TodoAction::Get { id } => {
                // Get todo using builder pattern
                if let Some(todo) = memories.recall(&format!("todos:item_{}", id)).as_type::<TodoItem>().await {
                    Ok(TodoReaction {
                        success: true,
                        message: format!("Found todo: {}", todo.title),
                        todo_item: Some(todo),
                        todo_list: vec![],
                    })
                } else {
                    Ok(TodoReaction {
                        success: false,
                        message: format!("Todo with id {} not found", id),
                        todo_item: None,
                        todo_list: vec![],
                    })
                }
            }
        }
        }
    }
}

impl Reaction for TodoReaction {
    fn remember(&self) -> Vec<MemoryOperation> {
        match self {
            TodoReaction::TodoCreated { todo } => {
                vec![
                    // Store the todo item
                    MemoryOperation::Create {
                        key: format!("todos:{}", todo.id),
                        value: MemoryValue::new(todo).unwrap(),
                    },
                    // Index by priority
                    MemoryOperation::Create {
                        key: format!("priority_index:{:?}:{}", todo.priority, todo.id),
                        value: MemoryValue::new(&todo.id).unwrap(),
                    },
                    // Index by completion status
                    MemoryOperation::Create {
                        key: format!("status_index:{}:{}", if todo.completed { "completed" } else { "pending" }, todo.id),
                        value: MemoryValue::new(&todo.id).unwrap(),
                    },
                    // Index by tags
                ] + todo.tags.iter().map(|tag| {
                    MemoryOperation::Create {
                        key: format!("tag_index:{}:{}", tag, todo.id),
                        value: MemoryValue::new(&todo.id).unwrap(),
                    }
                }).collect::<Vec<_>>() + vec![
                    // Create graph connections
                    MemoryOperation::Link {
                        from: "todos".to_string(),
                        to: "priorities".to_string(),
                        relation: "has_priority".to_string(),
                    },
                    MemoryOperation::Link {
                        from: "todos".to_string(),
                        to: "tags".to_string(),
                        relation: "has_tags".to_string(),
                    },
                ]
            }

            TodoReaction::TodoUpdated { todo, previous_version: _ } => {
                vec![
                    // Update the todo item
                    MemoryOperation::Update {
                        key: format!("todos:{}", todo.id),
                        value: MemoryValue::new(todo).unwrap(),
                    },
                    // Update priority index
                    MemoryOperation::Update {
                        key: format!("priority_index:{:?}:{}", todo.priority, todo.id),
                        value: MemoryValue::new(&todo.id).unwrap(),
                    },
                    // Update status index
                    MemoryOperation::Update {
                        key: format!("status_index:{}:{}", if todo.completed { "completed" } else { "pending" }, todo.id),
                        value: MemoryValue::new(&todo.id).unwrap(),
                    },
                ] + todo.tags.iter().map(|tag| {
                    MemoryOperation::Update {
                        key: format!("tag_index:{}:{}", tag, todo.id),
                        value: MemoryValue::new(&todo.id).unwrap(),
                    }
                }).collect::<Vec<_>>()
            }

            TodoReaction::TodoCompleted { todo } => {
                vec![
                    // Update the todo item
                    MemoryOperation::Update {
                        key: format!("todos:{}", todo.id),
                        value: MemoryValue::new(todo).unwrap(),
                    },
                    // Move from pending to completed index
                    MemoryOperation::Delete {
                        key: format!("status_index:pending:{}", todo.id),
                    },
                    MemoryOperation::Create {
                        key: format!("status_index:completed:{}", todo.id),
                        value: MemoryValue::new(&todo.id).unwrap(),
                    },
                ]
            }

            TodoReaction::TodoDeleted { todo_id, deleted_todo } => {
                vec![
                    // Delete the todo item
                    MemoryOperation::Delete {
                        key: format!("todos:{}", todo_id),
                    },
                    // Delete from priority index
                    MemoryOperation::Delete {
                        key: format!("priority_index:{:?}:{}", deleted_todo.priority, todo_id),
                    },
                    // Delete from status index
                    MemoryOperation::Delete {
                        key: format!("status_index:{}:{}", if deleted_todo.completed { "completed" } else { "pending" }, todo_id),
                    },
                ] + deleted_todo.tags.iter().map(|tag| {
                    MemoryOperation::Delete {
                        key: format!("tag_index:{}:{}", tag, todo_id),
                    }
                }).collect::<Vec<_>>()
            }

            TodoReaction::TagAdded { todo_id, tag } => {
                vec![
                    MemoryOperation::Create {
                        key: format!("tag_index:{}:{}", tag, todo_id),
                        value: MemoryValue::new(todo_id).unwrap(),
                    }
                ]
            }

            TodoReaction::TagRemoved { todo_id, tag } => {
                vec![
                    MemoryOperation::Delete {
                        key: format!("tag_index:{}:{}", tag, todo_id),
                    }
                ]
            }

            // Read-only operations don't need to remember anything
            TodoReaction::TodosListed { .. } => vec![],
            TodoReaction::StatsGenerated { .. } => vec![],
            TodoReaction::Error { .. } => vec![],
        }
    }
}

/// Todo Actor - demonstrates the builder pattern for extending actors
pub struct TodoActor;

impl Actor for TodoActor {
    type Action = TodoAction;

    async fn act(
        &self,
        action: Self::Action,
        actors: &Actors,
        memories: &ActorMemories,
    ) -> TamtilResult<Vec<u8>> {
        let reaction = action.act(actors, memories).await?;

        // Remember the reaction if it produces memory operations
        memories.remember_reaction(&reaction).await?;

        // Serialize and return the reaction
        let reaction_bytes = rkyv::api::high::to_bytes::<rkyv::rancor::Error>(&reaction)
            .map_err(|e| TamtilError::Serialization {
                message: format!("Failed to serialize reaction: {}", e)
            })?;

        Ok(reaction_bytes.to_vec())
    }
}

#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // Initialize tracing for observability
    tracing_subscriber::init();

    println!("🚀 TAMTIL Todo App - Demonstrating All Features");
    println!("================================================");

    // Create the actor system
    let actors = Actors::new().await?;

    // Build a todo actor using the builder pattern
    let todo_actor = ActorBuilder::new()
        .with_id("todo_manager".to_string())
        .build::<TodoActor>()
        .await?;

    // Spawn the actor in the system
    let actor_id = ActorId::new("todo_manager");
    actors.spawn(actor_id.clone(), todo_actor).await?;

    println!("\n📝 Creating some todos...");

    // Create some todos to demonstrate the system
    let create_actions = vec![
        TodoAction::CreateTodo {
            id: "todo_1".to_string(),
            title: "Learn TAMTIL".to_string(),
            description: "Study the zero-copy actor system".to_string(),
            priority: Priority::High,
            tags: vec!["learning".to_string(), "rust".to_string()],
        },
        TodoAction::CreateTodo {
            id: "todo_2".to_string(),
            title: "Build a web app".to_string(),
            description: "Create a todo app using TAMTIL".to_string(),
            priority: Priority::Medium,
            tags: vec!["development".to_string(), "web".to_string()],
        },
        TodoAction::CreateTodo {
            id: "todo_3".to_string(),
            title: "Write documentation".to_string(),
            description: "Document the TAMTIL features".to_string(),
            priority: Priority::Low,
            tags: vec!["documentation".to_string(), "writing".to_string()],
        },
    ];

    for action in create_actions {
        let reaction_bytes = actors.actor(&actor_id).act(action).await?;
        let reaction: TodoReaction = rkyv::api::high::from_bytes(&reaction_bytes)?;
        match reaction {
            TodoReaction::TodoCreated { todo } => {
                println!("✅ Created todo: {} (Priority: {:?})", todo.title, todo.priority);
            }
            TodoReaction::Error { message } => {
                println!("❌ Error: {}", message);
            }
            _ => {}
        }
    }

    println!("\n🏷️  Adding and removing tags...");

    // Demonstrate tag operations
    let tag_action = TodoAction::AddTag {
        todo_id: "todo_1".to_string(),
        tag: "urgent".to_string(),
    };

    let reaction_bytes = actors.actor(&actor_id).act(tag_action).await?;
    let reaction: TodoReaction = rkyv::api::high::from_bytes(&reaction_bytes)?;
    match reaction {
        TodoReaction::TagAdded { todo_id, tag } => {
            println!("✅ Added tag '{}' to todo {}", tag, todo_id);
        }
        _ => {}
    }

    println!("\n✅ Completing a todo...");

    // Complete a todo
    let complete_action = TodoAction::CompleteTodo {
        id: "todo_1".to_string(),
    };

    let reaction_bytes = actors.actor(&actor_id).act(complete_action).await?;
    let reaction: TodoReaction = rkyv::api::high::from_bytes(&reaction_bytes)?;
    match reaction {
        TodoReaction::TodoCompleted { todo } => {
            println!("✅ Completed todo: {}", todo.title);
        }
        _ => {}
    }

    println!("\n📊 Getting statistics...");

    // Get statistics to demonstrate graph queries
    let stats_action = TodoAction::GetTodoStats;
    let reaction_bytes = actors.actor(&actor_id).act(stats_action).await?;
    let reaction: TodoReaction = rkyv::api::high::from_bytes(&reaction_bytes)?;

    match reaction {
        TodoReaction::StatsGenerated {
            total_todos,
            completed_todos,
            pending_todos,
            priority_breakdown,
            tag_usage,
        } => {
            println!("📈 Todo Statistics:");
            println!("   Total todos: {}", total_todos);
            println!("   Completed: {}", completed_todos);
            println!("   Pending: {}", pending_todos);
            println!("   Priority breakdown: {:?}", priority_breakdown);
            println!("   Tag usage: {:?}", tag_usage);
        }
        _ => {}
    }

    println!("\n🔍 Listing todos by filter...");

    // List todos with different filters
    let filters = vec![
        ("All todos", TodoFilter::All),
        ("Completed todos", TodoFilter::Completed),
        ("Pending todos", TodoFilter::Pending),
        ("High priority todos", TodoFilter::ByPriority(Priority::High)),
        ("Learning-tagged todos", TodoFilter::ByTag("learning".to_string())),
    ];

    for (description, filter) in filters {
        let list_action = TodoAction::ListTodos { filter };
        let reaction_bytes = actors.actor(&actor_id).act(list_action).await?;
        let reaction: TodoReaction = rkyv::api::high::from_bytes(&reaction_bytes)?;

        match reaction {
            TodoReaction::TodosListed { todos, total_count, .. } => {
                println!("\n📋 {}: {} items", description, total_count);
                for todo in todos {
                    let status = if todo.completed { "✅" } else { "⏳" };
                    println!("   {} {} (Priority: {:?}, Tags: {:?})",
                             status, todo.title, todo.priority, todo.tags);
                }
            }
            _ => {}
        }
    }

    println!("\n🔄 Demonstrating fault tolerance...");

    // Get the actor's memories to demonstrate event sourcing
    let actor_proxy = actors.actor(&actor_id);
    if let Some(built_actor) = actor_proxy.get_built_actor().await {
        let reaction_log = built_actor.memories.reaction_log();
        let decided_idx = reaction_log.get_decided_idx().await;
        println!("📜 Reaction log has {} decided entries", decided_idx);

        // Demonstrate compaction (snapshot creation)
        let snapshot_data = b"snapshot_at_current_state".to_vec();
        reaction_log.compact(decided_idx / 2, snapshot_data).await?;

        let compacted_idx = reaction_log.get_compacted_idx().await;
        println!("📦 Compacted log up to index {}", compacted_idx);

        // Show that we can still access recent reactions
        let recent_reactions = reaction_log.get_reactions_from(compacted_idx).await;
        println!("🔄 {} recent reactions available for replay", recent_reactions.len());
    }

    println!("\n🎯 TAMTIL Features Demonstrated:");
    println!("✅ Action->Reaction pattern with business logic in actions");
    println!("✅ Event sourcing with reaction log for fault tolerance");
    println!("✅ Zero-copy serialization with rkyv");
    println!("✅ Graph-based memories with indexing and queries");
    println!("✅ Builder pattern for extending actors");
    println!("✅ Memory operations (Create, Update, Delete, Link)");
    println!("✅ Advanced filtering and analytics");
    println!("✅ Log compaction and recovery mechanisms");
    println!("✅ Type-safe actor communication");

    println!("\n🏁 Todo app demonstration complete!");

    Ok(())
}
