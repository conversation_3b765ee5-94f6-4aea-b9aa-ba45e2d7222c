/*!
 * Distributed Calculator Example - Demonstrates TAMTIL's fault tolerance
 * 
 * This example shows:
 * - 3 calculator actor instances for fault tolerance
 * - Consensus-based reaction ordering (simulated)
 * - Zero-copy memory access with rkyv
 * - Builder pattern for powerful recall operations
 * - ACID properties: all operations succeed or all fail
 */

use tamtil::*;
use rkyv::{Archive, Serialize, Deserialize};
use tokio;


/// Calculator actions with single-word naming
#[derive(Debug, Clone, Archive, Serialize, Deserialize)]
#[rkyv(derive(Debug))]
pub enum CalcAction {
    Add { value: i32 },
    Multiply { value: i32 },
    Subtract { value: i32 },
    Divide { value: i32 },
    Reset,
    Get,
}

/// Calculator reactions with single-word naming
#[derive(Debug, <PERSON>lone, Archive, Serialize, Deserialize)]
#[rkyv(derive(Debug))]
pub enum CalcReaction {
    Computed { result: i32, operation: String },
    Retrieved { result: i32 },
    Error { message: String },
}

impl Action for CalcAction {
    type Reaction = CalcReaction;
    
    fn act(
        &self,
        _actors: &Actors,
        memories: &ActorMemories,
    ) -> impl std::future::Future<Output = TamtilResult<Self::Reaction>> + Send {
        let action = self.clone();
        
        async move {
            match action {
                CalcAction::Add { value } => {
                    let current: Option<i32> = memories.recall("result").await;
                    let result = current.unwrap_or(0) + value;
                    Ok(CalcReaction::Computed {
                        result,
                        operation: format!("Added {}", value),
                    })
                }

                CalcAction::Multiply { value } => {
                    let current: Option<i32> = memories.recall("result").await;
                    let result = current.unwrap_or(1) * value;
                    Ok(CalcReaction::Computed {
                        result,
                        operation: format!("Multiplied by {}", value),
                    })
                }

                CalcAction::Subtract { value } => {
                    let current: Option<i32> = memories.recall("result").await;
                    let result = current.unwrap_or(0) - value;
                    Ok(CalcReaction::Computed {
                        result,
                        operation: format!("Subtracted {}", value),
                    })
                }

                CalcAction::Divide { value } => {
                    if value == 0 {
                        return Ok(CalcReaction::Error {
                            message: "Division by zero".to_string(),
                        });
                    }
                    
                    let current: Option<i32> = memories.recall("result").await;
                    let result = current.unwrap_or(0) / value;
                    Ok(CalcReaction::Computed {
                        result,
                        operation: format!("Divided by {}", value),
                    })
                }

                CalcAction::Reset => {
                    Ok(CalcReaction::Computed {
                        result: 0,
                        operation: "Reset".to_string(),
                    })
                }

                CalcAction::Get => {
                    let result: Option<i32> = memories.recall("result").await;
                    Ok(CalcReaction::Retrieved {
                        result: result.unwrap_or(0),
                    })
                }
            }
        }
    }
}

impl Reaction for CalcReaction {
    fn remember(&self) -> Vec<MemoryOperation> {
        match self {
            CalcReaction::Computed { result, operation } => {
                vec![
                    MemoryOperation::Update {
                        key: "result".to_string(),
                        value: MemoryValue::new(result).unwrap(),
                    },
                    MemoryOperation::Create {
                        key: format!("history:{}", chrono::Utc::now().timestamp_millis()),
                        value: MemoryValue::new(operation).unwrap(),
                    },
                ]
            }
            CalcReaction::Retrieved { .. } => {
                // Read-only operations don't modify state
                vec![]
            }
            CalcReaction::Error { .. } => {
                // Error reactions don't modify state
                vec![]
            }
        }
    }
}

/// Distributed calculator cluster with fault tolerance
pub struct DistributedCalc {
    actors: Actors,
    instances: Vec<ActorId>, // Actor IDs for the 3 instances
}

impl DistributedCalc {
    /// Create a new distributed calculator with 3 instances
    pub async fn new() -> TamtilResult<Self> {
        let actors = Actors::new().await?;
        let mut instances = Vec::new();

        // Create 3 calculator instances for fault tolerance
        for i in 1..=3 {
            let instance_id = ActorId::new(format!("calc_instance_{}", i));
            let calc_actor = ActorBuilder::new(instance_id.clone()).await?.build::<CalcAction>();
            actors.spawn(instance_id.clone(), calc_actor).await?;
            instances.push(instance_id);
        }

        Ok(Self { actors, instances })
    }

    /// Execute an action on all instances (consensus simulation)
    pub async fn execute(&self, action: CalcAction) -> TamtilResult<CalcReaction> {
        println!("🔄 Executing {:?} on {} instances", action, self.instances.len());
        
        let mut results = Vec::new();
        let mut successful_instances = 0;

        // Execute on all instances
        for (i, instance_id) in self.instances.iter().enumerate() {
            match self.actors.actor(instance_id).act(action.clone()).await {
                Ok(reaction_bytes) => {
                    let reaction: CalcReaction = rkyv::api::high::from_bytes::<CalcReaction, rkyv::rancor::Error>(&reaction_bytes)
                        .map_err(|e: rkyv::rancor::Error| TamtilError::Deserialization {
                            message: format!("Failed to deserialize reaction: {}", e)
                        })?;
                    
                    println!("✅ Instance {} succeeded: {:?}", i + 1, reaction);
                    results.push(reaction);
                    successful_instances += 1;
                }
                Err(e) => {
                    println!("❌ Instance {} failed: {}", i + 1, e);
                }
            }
        }

        // Consensus: require majority (2 out of 3) for success
        if successful_instances >= 2 {
            println!("✅ Consensus achieved ({}/{} instances)", successful_instances, self.instances.len());
            Ok(results.into_iter().next().unwrap()) // Return first successful result
        } else {
            Err(TamtilError::ConsensusFailure {
                message: format!("Only {}/{} instances succeeded", successful_instances, self.instances.len())
            })
        }
    }

    /// Demonstrate fault tolerance by simulating instance failure
    pub async fn simulate_failure(&mut self, instance_index: usize) -> TamtilResult<()> {
        if instance_index < self.instances.len() {
            let failed_instance = self.instances.remove(instance_index);
            println!("💥 Simulated failure of instance: {}", failed_instance);
        }
        Ok(())
    }

    /// Get current state from all healthy instances
    pub async fn get_state(&self) -> TamtilResult<Vec<i32>> {
        let mut states = Vec::new();
        
        for (i, instance_id) in self.instances.iter().enumerate() {
            match self.actors.actor(instance_id).act(CalcAction::Get).await {
                Ok(reaction_bytes) => {
                    let reaction: CalcReaction = rkyv::api::high::from_bytes(&reaction_bytes)
                        .map_err(|e| TamtilError::Deserialization {
                            message: format!("Failed to deserialize reaction: {}", e)
                        })?;
                    
                    if let CalcReaction::Retrieved { result } = reaction {
                        states.push(result);
                        println!("📊 Instance {} state: {}", i + 1, result);
                    }
                }
                Err(e) => {
                    println!("❌ Failed to get state from instance {}: {}", i + 1, e);
                }
            }
        }
        
        Ok(states)
    }
}

#[tokio::main]
async fn main() -> TamtilResult<()> {
    println!("🚀 Starting Distributed Calculator Demo");
    println!("📋 This demonstrates TAMTIL's fault tolerance with 3 actor instances\n");

    // Create distributed calculator
    let mut calc = DistributedCalc::new().await?;
    
    // Demonstrate normal operations
    println!("=== Normal Operations ===");
    calc.execute(CalcAction::Add { value: 10 }).await?;
    calc.execute(CalcAction::Multiply { value: 3 }).await?;
    calc.execute(CalcAction::Subtract { value: 5 }).await?;
    
    println!("\n=== Current State ===");
    let states = calc.get_state().await?;
    println!("All instances should show: 25 (10 * 3 - 5)");
    
    // Simulate failure
    println!("\n=== Fault Tolerance Test ===");
    calc.simulate_failure(0).await?; // Remove first instance
    
    // Continue operations with reduced instances
    println!("Continuing with {} instances...", calc.instances.len());
    calc.execute(CalcAction::Add { value: 5 }).await?;
    
    println!("\n=== Final State ===");
    let final_states = calc.get_state().await?;
    println!("Remaining instances should show: 30 (25 + 5)");
    
    // Demonstrate consensus failure
    println!("\n=== Consensus Failure Test ===");
    calc.simulate_failure(0).await?; // Remove another instance (only 1 left)
    
    match calc.execute(CalcAction::Add { value: 1 }).await {
        Ok(_) => println!("❌ This shouldn't happen - not enough instances for consensus"),
        Err(e) => println!("✅ Expected consensus failure: {}", e),
    }
    
    println!("\n🎉 Distributed Calculator Demo Complete!");
    println!("Key features demonstrated:");
    println!("- ✅ Fault tolerance with multiple instances");
    println!("- ✅ Consensus-based operations (2/3 majority)");
    println!("- ✅ ACID properties (atomic operations)");
    println!("- ✅ Zero-copy memory access with rkyv");
    println!("- ✅ Event sourcing with reaction logs");
    
    Ok(())
}
