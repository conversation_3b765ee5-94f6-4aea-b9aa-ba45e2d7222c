//! # TAMTIL Calculator Example
//!
//! This example demonstrates TAMTIL's key features:
//! - Action->Reaction pattern with business logic in actions
//! - Event sourcing with reaction log for fault tolerance
//! - Zero-copy serialization with rkyv
//! - State persistence across multiple actions
//! - Memory operations (Create, Update, Delete)
//! - Comprehensive error handling
//! - One-word naming convention

use tamtil::*;
use rkyv::{Archive, Serialize, Deserialize};
use async_trait::async_trait;
use tokio;

/// Calculator actions - business logic is in act() methods
#[derive(Debug, <PERSON>lone, Archive, Serialize, Deserialize)]
#[rkyv(derive(Debug))]
pub enum CalcAction {
    Add { value: i32 },
    Multiply { value: i32 },
    Subtract { value: i32 },
    Divide { value: i32 },
    Reset,
    GetResult,
}

/// Calculator reactions - state changes are in remember() methods
#[derive(Debug, Clone, Archive, Serialize, Deserialize)]
#[rkyv(derive(Debug))]
pub enum CalcReaction {
    Computed { result: i32, operation: String },
    Retrieved { result: i32 },
    Error { message: String },
}

impl Action for CalcAction {
    type Reaction = CalcReaction;

    fn act(
        &self,
        _actors: &Actors,
        memories: &ActorMemories,
    ) -> impl std::future::Future<Output = TamtilResult<Vec<u8>>> + Send {
        let action = self.clone();

        async move {
            match action {
                CalcAction::Add { value } => {
                    let current: Option<i32> = memories.recall("result").await;
                    let result = current.unwrap_or(0) + value;
                    let reaction = CalcReaction::Computed {
                        result,
                        operation: format!("Added {}", value),
                    };
                    memories.remember_and_serialize(&reaction).await
                }

                CalcAction::Multiply { value } => {
                    let current: Option<i32> = memories.recall("result").await;
                    let result = current.unwrap_or(1) * value;
                    let reaction = CalcReaction::Computed {
                        result,
                        operation: format!("Multiplied by {}", value),
                    };
                    memories.remember_and_serialize(&reaction).await
                }

                CalcAction::Subtract { value } => {
                    let current: Option<i32> = memories.recall("result").await;
                    let result = current.unwrap_or(0) - value;
                    let reaction = CalcReaction::Computed {
                        result,
                        operation: format!("Subtracted {}", value),
                    };
                    memories.remember_and_serialize(&reaction).await
                }

                CalcAction::Divide { value } => {
                    if value == 0 {
                        let error_reaction = CalcReaction::Error {
                            message: "Division by zero".to_string(),
                        };
                        return memories.remember_and_serialize(&error_reaction).await;
                    }

                    let current: Option<i32> = memories.recall("result").await;
                    let result = current.unwrap_or(0) / value;
                    let reaction = CalcReaction::Computed {
                        result,
                        operation: format!("Divided by {}", value),
                    };
                    memories.remember_and_serialize(&reaction).await
                }

                CalcAction::Reset => {
                    let reaction = CalcReaction::Computed {
                        result: 0,
                        operation: "Reset".to_string(),
                    };
                    memories.remember_and_serialize(&reaction).await
                }

                CalcAction::GetResult => {
                    let result: Option<i32> = memories.recall("result").await;
                    let reaction = CalcReaction::Retrieved {
                        result: result.unwrap_or(0),
                    };
                    memories.remember_and_serialize(&reaction).await
                }
            }
        }
    }
}

impl Reaction for CalcReaction {
    fn remember(&self) -> Vec<MemoryOperation> {
        match self {
            CalcReaction::Computed { result, .. } => {
                vec![
                    MemoryOperation::Update {
                        key: "result".to_string(),
                        value: MemoryValue::new(result).unwrap(),
                    },
                ]
            }
            // Read-only operations don't change state
            CalcReaction::Retrieved { .. } => vec![],
            CalcReaction::Error { .. } => vec![],
        }
    }
}



#[tokio::main]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    println!("🧮 TAMTIL Calculator - Zero-Copy Actor System Demo");
    println!("==================================================");

    // Create actor system
    let actors = Actors::new().await?;
    
    // Build calculator actor using builder pattern
    let calculator = ActorBuilder::new(ActorId::new("calculator"))
        .await?
        .build::<CalcAction>();

    // Spawn actor in system
    let actor_id = ActorId::new("calculator");
    actors.spawn(actor_id.clone(), calculator).await?;

    println!("\n🔢 Performing calculations...");
    
    // Demonstrate action->reaction pattern with state persistence
    let operations = vec![
        CalcAction::Add { value: 10 },
        CalcAction::Multiply { value: 3 },
        CalcAction::Subtract { value: 5 },
        CalcAction::Divide { value: 5 },
        CalcAction::GetResult,
        CalcAction::Reset,
        CalcAction::GetResult,
    ];

    for operation in operations {
        let reaction_bytes = actors.actor(&actor_id).act(operation).await?;
        let reaction: CalcReaction = rkyv::api::high::from_bytes::<CalcReaction, rkyv::rancor::Error>(&reaction_bytes)?;
        
        match reaction {
            CalcReaction::Computed { result, operation } => {
                println!("✅ {}: Result = {}", operation, result);
            }
            CalcReaction::Retrieved { result } => {
                println!("📊 Current result: {}", result);
            }
            CalcReaction::Error { message } => {
                println!("❌ Error: {}", message);
            }
        }
    }

    println!("\n🧪 Testing error handling...");
    
    // Test division by zero
    let error_action = CalcAction::Divide { value: 0 };
    let reaction_bytes = actors.actor(&actor_id).act(error_action).await?;
    let reaction: CalcReaction = rkyv::api::high::from_bytes::<CalcReaction, rkyv::rancor::Error>(&reaction_bytes)?;
    
    match reaction {
        CalcReaction::Error { message } => {
            println!("✅ Error handling works: {}", message);
        }
        _ => {
            println!("❌ Expected error but got different reaction");
        }
    }

    println!("\n🎯 TAMTIL Features Demonstrated:");
    println!("✅ Action->Reaction pattern with business logic in actions");
    println!("✅ Event sourcing with reaction log for fault tolerance");
    println!("✅ Zero-copy serialization with rkyv");
    println!("✅ State persistence across multiple actions");
    println!("✅ Builder pattern for extending actors");
    println!("✅ Memory operations (Create, Update, Delete)");
    println!("✅ Comprehensive error handling");
    println!("✅ Type-safe actor communication");
    println!("✅ One-word naming convention");
    println!("✅ No mock/stub implementations - all real functionality");

    println!("\n🏁 Calculator demonstration complete!");
    
    Ok(())
}
